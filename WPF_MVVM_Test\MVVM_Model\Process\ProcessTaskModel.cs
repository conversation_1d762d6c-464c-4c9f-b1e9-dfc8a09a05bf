using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Collections.Generic;

namespace WPF_MVVM_Test.MVVM_Model.Process
{
    /// <summary>
    /// 工艺流程任务模型
    /// </summary>
    public class ProcessTaskModel : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private int _sequenceNo;
        private string _taskId = string.Empty;
        private string _taskName = string.Empty;
        private string _stationName = string.Empty;
        private string _stationId = string.Empty;
        private decimal _plannedQuantity;
        private DateTime? _plannedStartTime;
        private DateTime? _plannedEndTime;
        private string _taskColor = "#1890FF";
        private bool _isSelected = false;
        private string _processStepId = string.Empty;
        private string _processRouteId = string.Empty;
        private string _processRouteName = string.Empty;
        private string _processType = string.Empty;
        private decimal _planDuration;
        private decimal? _actualDuration;
        private string _status = "未开工";
        private string _productionOrderId = string.Empty;
        private int _priority = 2;
        private string _remarks = string.Empty;

        /// <summary>
        /// 任务ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        /// <summary>
        /// 序号
        /// </summary>
        [JsonPropertyName("sequenceNo")]
        public int SequenceNo
        {
            get => _sequenceNo;
            set
            {
                if (_sequenceNo != value)
                {
                    _sequenceNo = value;
                    OnPropertyChanged(nameof(SequenceNo));
                }
            }
        }

        /// <summary>
        /// 任务编号
        /// </summary>
        [JsonPropertyName("taskId")]
        public string TaskId
        {
            get => _taskId;
            set
            {
                if (_taskId != value)
                {
                    _taskId = value;
                    OnPropertyChanged(nameof(TaskId));
                }
            }
        }

        /// <summary>
        /// 任务名称
        /// </summary>
        [JsonPropertyName("taskName")]
        public string TaskName
        {
            get => _taskName;
            set
            {
                if (_taskName != value)
                {
                    _taskName = value;
                    OnPropertyChanged(nameof(TaskName));
                }
            }
        }

        /// <summary>
        /// 站点名称
        /// </summary>
        [JsonPropertyName("stationName")]
        public string StationName
        {
            get => _stationName;
            set
            {
                if (_stationName != value)
                {
                    _stationName = value;
                    OnPropertyChanged(nameof(StationName));
                }
            }
        }

        /// <summary>
        /// 站点编号
        /// </summary>
        [JsonPropertyName("stationId")]
        public string StationId
        {
            get => _stationId;
            set
            {
                if (_stationId != value)
                {
                    _stationId = value;
                    OnPropertyChanged(nameof(StationId));
                }
            }
        }

        /// <summary>
        /// 计划数量
        /// </summary>
        [JsonPropertyName("plannedQuantity")]
        public decimal PlannedQuantity
        {
            get => _plannedQuantity;
            set
            {
                if (_plannedQuantity != value)
                {
                    _plannedQuantity = value;
                    OnPropertyChanged(nameof(PlannedQuantity));
                }
            }
        }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        [JsonPropertyName("plannedStartTime")]
        public DateTime? PlannedStartTime
        {
            get => _plannedStartTime;
            set
            {
                if (_plannedStartTime != value)
                {
                    _plannedStartTime = value;
                    OnPropertyChanged(nameof(PlannedStartTime));
                }
            }
        }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        [JsonPropertyName("plannedEndTime")]
        public DateTime? PlannedEndTime
        {
            get => _plannedEndTime;
            set
            {
                if (_plannedEndTime != value)
                {
                    _plannedEndTime = value;
                    OnPropertyChanged(nameof(PlannedEndTime));
                }
            }
        }

        /// <summary>
        /// 任务颜色
        /// </summary>
        [JsonPropertyName("taskColor")]
        public string TaskColor
        {
            get => _taskColor;
            set
            {
                if (_taskColor != value)
                {
                    _taskColor = value;
                    OnPropertyChanged(nameof(TaskColor));
                }
            }
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        /// <summary>
        /// 工序ID
        /// </summary>
        [JsonPropertyName("processStepId")]
        public string ProcessStepId
        {
            get => _processStepId;
            set
            {
                if (_processStepId != value)
                {
                    _processStepId = value;
                    OnPropertyChanged(nameof(ProcessStepId));
                }
            }
        }

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        [JsonPropertyName("processRouteId")]
        public string ProcessRouteId
        {
            get => _processRouteId;
            set
            {
                if (_processRouteId != value)
                {
                    _processRouteId = value;
                    OnPropertyChanged(nameof(ProcessRouteId));
                }
            }
        }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        [JsonPropertyName("processRouteName")]
        public string ProcessRouteName
        {
            get => _processRouteName;
            set
            {
                if (_processRouteName != value)
                {
                    _processRouteName = value;
                    OnPropertyChanged(nameof(ProcessRouteName));
                }
            }
        }

        /// <summary>
        /// 工艺类型
        /// </summary>
        [JsonPropertyName("processType")]
        public string ProcessType
        {
            get => _processType;
            set
            {
                if (_processType != value)
                {
                    _processType = value;
                    OnPropertyChanged(nameof(ProcessType));
                }
            }
        }

        /// <summary>
        /// 计划用时（小时）
        /// </summary>
        [JsonPropertyName("planDuration")]
        public decimal PlanDuration
        {
            get => _planDuration;
            set
            {
                if (_planDuration != value)
                {
                    _planDuration = value;
                    OnPropertyChanged(nameof(PlanDuration));
                }
            }
        }

        /// <summary>
        /// 实际用时（小时）
        /// </summary>
        [JsonPropertyName("actualDuration")]
        public decimal? ActualDuration
        {
            get => _actualDuration;
            set
            {
                if (_actualDuration != value)
                {
                    _actualDuration = value;
                    OnPropertyChanged(nameof(ActualDuration));
                }
            }
        }

        /// <summary>
        /// 任务状态
        /// </summary>
        [JsonPropertyName("status")]
        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        /// <summary>
        /// 生产工单ID
        /// </summary>
        [JsonPropertyName("productionOrderId")]
        public string ProductionOrderId
        {
            get => _productionOrderId;
            set
            {
                if (_productionOrderId != value)
                {
                    _productionOrderId = value;
                    OnPropertyChanged(nameof(ProductionOrderId));
                }
            }
        }

        /// <summary>
        /// 优先级
        /// </summary>
        [JsonPropertyName("priority")]
        public int Priority
        {
            get => _priority;
            set
            {
                if (_priority != value)
                {
                    _priority = value;
                    OnPropertyChanged(nameof(Priority));
                }
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remarks")]
        public string Remarks
        {
            get => _remarks;
            set
            {
                if (_remarks != value)
                {
                    _remarks = value;
                    OnPropertyChanged(nameof(Remarks));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 工艺流程任务API响应模型
    /// </summary>
    public class ProcessTaskApiResponse
    {
        [JsonPropertyName("data")]
        public List<ProcessTaskModel> Data { get; set; } = new List<ProcessTaskModel>();

        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量添加任务请求模型
    /// </summary>
    public class BatchAddWorkOrderTasksRequest
    {
        [JsonPropertyName("tasks")]
        public List<WorkOrderTaskRequest> Tasks { get; set; } = new List<WorkOrderTaskRequest>();
    }

    /// <summary>
    /// 带DTO包装的批量添加任务请求模型
    /// </summary>
    public class BatchAddWorkOrderTasksDtoRequest
    {
        [JsonPropertyName("dto")]
        public BatchAddWorkOrderTasksRequest Dto { get; set; } = new BatchAddWorkOrderTasksRequest();
    }

    /// <summary>
    /// 工单任务请求模型
    /// </summary>
    public class WorkOrderTaskRequest
    {
        [JsonPropertyName("sequenceNumber")]
        public int SequenceNumber { get; set; }

        [JsonPropertyName("taskNumber")]
        public string TaskNumber { get; set; } = string.Empty;

        [JsonPropertyName("taskName")]
        public string TaskName { get; set; } = string.Empty;

        [JsonPropertyName("productionOrderId")]
        public string ProductionOrderId { get; set; } = string.Empty;

        [JsonPropertyName("stationName")]
        public string StationName { get; set; } = string.Empty;

        [JsonPropertyName("processCode")]
        public string ProcessCode { get; set; } = string.Empty;

        [JsonPropertyName("processName")]
        public string ProcessName { get; set; } = string.Empty;

        [JsonPropertyName("processFlow")]
        public string ProcessFlow { get; set; } = string.Empty;

        [JsonPropertyName("processType")]
        public string ProcessType { get; set; } = string.Empty;

        [JsonPropertyName("taskColor")]
        public string TaskColor { get; set; } = string.Empty;

        [JsonPropertyName("planQuantity")]
        public decimal PlanQuantity { get; set; }

        [JsonPropertyName("planStartTime")]
        public DateTime PlanStartTime { get; set; }

        [JsonPropertyName("planEndTime")]
        public DateTime PlanEndTime { get; set; }

        [JsonPropertyName("planDuration")]
        public decimal PlanDuration { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        [JsonPropertyName("processRouteId")]
        public Guid? ProcessRouteId { get; set; }

        [JsonPropertyName("processStepId")]
        public Guid? ProcessStepId { get; set; }

        [JsonPropertyName("priority")]
        public int Priority { get; set; }

        [JsonPropertyName("remarks")]
        public string Remarks { get; set; } = string.Empty;
    }
} 