<UserControl x:Class="WPF_MVVM_Test.MVVM_View.LMZWorkSchedule.WorkReportQualityDetail"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel.LMZWorkSchedule"
             xmlns:converters="clr-namespace:WPF_MVVM_Test.MVVM_View.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000">

    <UserControl.Resources>
        <converters:SelectedCountConverter x:Key="SelectedCountConverter"/>
    </UserControl.Resources>



    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/><!-- 标题和返回按钮 -->
            <RowDefinition Height="*"/><!-- 详情内容 -->
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 返回按钮 -->
                <Button Grid.Column="0"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding BackCommand}"
                        Height="40"
                        Width="80"
                        Margin="0,0,15,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ArrowLeft" 
                                                 Width="16" 
                                                 Height="16" 
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,5,0"/>
                        <TextBlock Text="返回" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- 标题和操作按钮 -->
                <Grid Grid.Column="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 标题 -->
                    <TextBlock Grid.Column="0"
                               Text="报工质检详情"
                               FontSize="20"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               Foreground="#2E3440"/>
                    
                    <!-- 保存按钮 -->
                    <Button Grid.Column="1"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding SaveCommand}"
                            Height="40"
                            Width="100"
                            Background="#4CAF50"
                            BorderBrush="#4CAF50"
                            IsEnabled="{Binding CanSave}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" 
                                                     Width="16" 
                                                     Height="16" 
                                                     VerticalAlignment="Center"
                                                     Margin="0,0,5,0"/>
                            <TextBlock Text="保存" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- 详情内容 -->
        <ScrollViewer Grid.Row="1" Margin="10,0,10,10" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 基础信息 -->
                <materialDesign:Card Margin="0,0,0,10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="基础信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2E3440"
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 第一行 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="ID" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.Id}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="序号" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.SequenceNumber}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,15">
                                <TextBlock Text="任务编号" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.TaskNumber}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第二行 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="检验代码" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.InspectionCode}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="检验名称" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.InspectionName}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,0,15">
                                <TextBlock Text="检验类型" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.InspectionType}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第三行 -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="状态" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <Border CornerRadius="10" Padding="8,4" HorizontalAlignment="Left" Margin="0,5,0,0">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CurrentItem.Status}" Value="待检验">
                                                    <Setter Property="Background" Value="#FFF3E0"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CurrentItem.Status}" Value="检验中">
                                                    <Setter Property="Background" Value="#E8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CurrentItem.Status}" Value="已完成">
                                                    <Setter Property="Background" Value="#E3F2FD"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CurrentItem.Status}" Value="不合格">
                                                    <Setter Property="Background" Value="#FFEBEE"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Text="{Binding CurrentItem.Status}" FontSize="12">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Foreground" Value="#1976D2"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding CurrentItem.Status}" Value="待检验">
                                                        <Setter Property="Foreground" Value="#F57C00"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding CurrentItem.Status}" Value="检验中">
                                                        <Setter Property="Foreground" Value="#388E3C"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding CurrentItem.Status}" Value="已完成">
                                                        <Setter Property="Foreground" Value="#1976D2"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding CurrentItem.Status}" Value="不合格">
                                                        <Setter Property="Foreground" Value="#D32F2F"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="0,0,10,0">
                                <TextBlock Text="总体结果" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OverallResult}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 产品和工序信息 -->
                <materialDesign:Card Margin="0,0,0,10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="产品和工序信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2E3440"
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 第一行 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="产品ID" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.ProductId}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="工序步骤ID" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.ProcessStepId}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,15">
                                <TextBlock Text="工序名称" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.ProcessName}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第二行 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="工位ID" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.StationId}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="班组ID" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.TeamId}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,0,15">
                                <TextBlock Text="规格" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.Specification}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第三行 -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="单位" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.Unit}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 人员信息 -->
                <materialDesign:Card Margin="0,0,0,10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="人员信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2E3440"
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 第一行 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="报工员ID" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.ReporterId}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="检验员ID" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.InspectorId}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,15">
                                <TextBlock Text="检验员姓名" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.InspectorName}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第二行 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="检验部门" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.InspectionDepartment}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 数量信息 -->
                <materialDesign:Card Margin="0,0,0,10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="数量信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2E3440"
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 第一行 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="报工数量" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.ReportedQuantity}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="检验数量" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.TestedQuantity}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,15">
                                <TextBlock Text="合格数量" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.QualifiedQuantity}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第二行 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="不合格数量" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.UnqualifiedQuantity}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 时间信息 -->
                <materialDesign:Card Margin="0,0,0,10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="时间信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2E3440"
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 第一行 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="报工时间" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.ReportTime, StringFormat=yyyy-MM-dd HH:mm}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,0">
                                <TextBlock Text="检验时间" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.InspectionTime, StringFormat=yyyy-MM-dd HH:mm}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,0">
                                <TextBlock Text="计划开始时间" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.PlanStartTime, StringFormat=yyyy-MM-dd HH:mm}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 工单信息 -->
                <materialDesign:Card Margin="0,0,0,10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="工单信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2E3440"
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 第一行 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="工单编号" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderNumber}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="工单名称" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderName}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,15">
                                <TextBlock Text="生产计划ID" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.ProductionPlanId}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第二行 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="工单计划数量" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderPlanQuantity}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="工单实际数量" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderActualQuantity}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,0,15">
                                <TextBlock Text="工单状态" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderStatus}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第三行 -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="工单计划开始时间" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderPlanStartTime, StringFormat=yyyy-MM-dd HH:mm}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="工单计划结束时间" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderPlanEndTime, StringFormat=yyyy-MM-dd HH:mm}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>

                            <!-- 第四行 -->
                            <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="工单实际开始时间" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderActualStartTime, StringFormat=yyyy-MM-dd HH:mm}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="3" Grid.Column="1" Margin="0,0,10,15">
                                <TextBlock Text="工单实际结束时间" FontWeight="Bold" FontSize="12" Foreground="#666"/>
                                <TextBlock Text="{Binding CurrentItem.OrderActualEndTime, StringFormat=yyyy-MM-dd HH:mm}" FontSize="14" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 质检方案选择 -->
                <materialDesign:Card Margin="0,0,0,10" Padding="20">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0"
                                       Text="质检方案选择" 
                                       FontSize="16" 
                                       FontWeight="Bold" 
                                       Foreground="#2E3440"
                                       VerticalAlignment="Center"/>
                            
                            <Button Grid.Column="1"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Command="{Binding SelectAllCommand}"
                                    Height="32"
                                    Padding="10,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Width="16" 
                                                             Height="16" 
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,5,0">
                                        <materialDesign:PackIcon.Style>
                                            <Style TargetType="materialDesign:PackIcon">
                                                <Setter Property="Kind" Value="CheckboxBlankOutline"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsAllSelected}" Value="True">
                                                        <Setter Property="Kind" Value="CheckboxMarked"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </materialDesign:PackIcon.Style>
                                    </materialDesign:PackIcon>
                                    <TextBlock Text="全选" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                        
                        <!-- 加载提示 -->
                        <StackPanel HorizontalAlignment="Center" Margin="0,20">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsLoadingPlans}" Value="True">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                         IsIndeterminate="True"
                                         Width="40"
                                         Height="40"/>
                            <TextBlock Text="正在加载质检方案..." 
                                       HorizontalAlignment="Center"
                                       Margin="0,10,0,0"
                                       Foreground="#666"/>
                        </StackPanel>
                        
                        <!-- 质检方案列表 -->
                        <DataGrid ItemsSource="{Binding QualityInspectionPlans}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="False"
                                  CanUserResizeRows="False"
                                  SelectionMode="Extended"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  MaxHeight="300">
                            <DataGrid.Style>
                                <Style TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsLoadingPlans}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.Style>
                            
                            <DataGrid.Columns>
                                <!-- 选择列 -->
                                <DataGridTemplateColumn Header="选择" Width="60">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                
                                <!-- 方案名称 -->
                                <DataGridTextColumn Header="方案名称" 
                                                    Binding="{Binding PlanName}" 
                                                    Width="150"
                                                    IsReadOnly="True"/>
                                
                                <!-- 方案编号 -->
                                <DataGridTextColumn Header="方案编号" 
                                                    Binding="{Binding PlanCode}" 
                                                    Width="150"
                                                    IsReadOnly="True"/>
                                
                                <!-- 检测类别 -->
                                <DataGridTextColumn Header="检测类别" 
                                                    Binding="{Binding DetectionCategory}" 
                                                    Width="120"
                                                    IsReadOnly="True"/>
                                
                                <!-- 检测类型 -->
                                <DataGridTextColumn Header="检测类型" 
                                                    Binding="{Binding DetectionTypes}" 
                                                    Width="120"
                                                    IsReadOnly="True"/>
                                
                                <!-- 状态 -->
                                <DataGridTemplateColumn Header="状态" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border CornerRadius="10" 
                                                    Padding="8,4" 
                                                    HorizontalAlignment="Center">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="禁用">
                                                                <Setter Property="Background" Value="#FFEBEE"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock Text="{Binding Status}" FontSize="12">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="Foreground" Value="#388E3C"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Status}" Value="禁用">
                                                                    <Setter Property="Foreground" Value="#D32F2F"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                
                                <!-- 版本 -->
                                <DataGridTextColumn Header="版本" 
                                                    Binding="{Binding Version}" 
                                                    Width="60"
                                                    IsReadOnly="True"/>
                                
                                <!-- 生效时间 -->
                                <DataGridTextColumn Header="生效时间" 
                                                    Binding="{Binding EffectiveTime, StringFormat=yyyy-MM-dd HH:mm}" 
                                                    Width="140"
                                                    IsReadOnly="True"/>
                                
                                <!-- 备注 -->
                                <DataGridTextColumn Header="备注" 
                                                    Binding="{Binding Remark}" 
                                                    Width="*"
                                                    IsReadOnly="True"/>
                            </DataGrid.Columns>
                        </DataGrid>
                        
                        <!-- 选择统计 -->
                        <TextBlock Text="{Binding QualityInspectionPlans, Converter={StaticResource SelectedCountConverter}}"
                                   Margin="0,10,0,0"
                                   FontSize="12"
                                   Foreground="#666">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Visible"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsLoadingPlans}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 备注信息 -->
                <materialDesign:Card Margin="0,0,0,10" Padding="20">
                    <StackPanel>
                        <TextBlock Text="备注信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="#2E3440"
                                   Margin="0,0,0,15"/>
                        
                        <TextBox Text="{Binding CurrentItem.Remark, Mode=OneWay}"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 IsReadOnly="True"
                                 TextWrapping="Wrap"
                                 MinHeight="80"
                                 VerticalContentAlignment="Top"
                                 materialDesign:HintAssist.Hint="备注内容"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>