# 数据字段问题排查指南

## 问题描述

任务统计显示有数据，但点击测试按钮或确认排产时仍然报错"工单任务列表不能为空"。这表明问题可能在于某些关键字段没有正确获取到数据。

## 问题分析

### 可能的原因

1. **字段映射问题** - 前端显示的数据与发送给后端的数据不一致
2. **空值处理问题** - 某些字段可能为空或null，导致后端验证失败
3. **数据类型问题** - 某些字段的数据类型不正确
4. **GUID格式问题** - processRouteId和processStepId可能不是有效的GUID格式

## 排查步骤

### 1. 使用"🔍 字段检查"按钮
1. 打开排产对话框
2. 点击"🔍 字段检查"按钮
3. 查看检查结果，确认哪些字段有问题

### 2. 查看调试日志
1. 打开Visual Studio
2. 运行应用程序
3. 打开"输出"窗口
4. 点击"✅ 确定排产"按钮
5. 查看详细的调试日志输出

### 3. 检查关键字段

#### 必需的基础字段
- **生产工单ID** (`_originalOrder.Id`)
- **工艺路线编号** (`ProcessRouteNumber`)
- **工艺路线名称** (`ProcessRouteName`)

#### 必需的任务字段
- **任务编号** (`TaskNumber`)
- **任务名称** (`TaskName`)
- **生产工单ID** (`ProductionOrderId`)
- **工位名称** (`StationName`)
- **工序代码** (`ProcessCode`)
- **工序名称** (`ProcessName`)
- **工艺流程** (`ProcessFlow`)
- **工序类型** (`ProcessType`)
- **任务颜色** (`TaskColor`)
- **计划数量** (`PlanQuantity`)
- **计划开始时间** (`PlanStartTime`)
- **计划结束时间** (`PlanEndTime`)
- **计划时长** (`PlanDuration`)
- **任务状态** (`Status`)
- **工艺路线ID** (`ProcessRouteId`) - 必须是有效的GUID
- **工序步骤ID** (`ProcessStepId`) - 必须是有效的GUID
- **优先级** (`Priority`)
- **备注** (`Remarks`)

## 常见问题及解决方案

### 问题1: 生产工单ID为空
**症状**: 字段检查显示"生产工单ID为空"
**解决方案**:
- 检查`_originalOrder`对象是否正确初始化
- 确认工单数据是否正确加载

### 问题2: 工艺路线信息为空
**症状**: 字段检查显示"工艺路线编号为空"或"工艺路线名称为空"
**解决方案**:
- 检查`LoadProcessRoutingAsync`方法是否正确执行
- 确认API返回的工艺路线数据是否正确

### 问题3: 工序数据为空
**症状**: 字段检查显示"工序列表为空"
**解决方案**:
- 检查`LoadProcessStepsAsync`方法是否正确执行
- 确认API返回的工序数据是否正确

### 问题4: 任务数据字段为空
**症状**: 字段检查显示某个工序的任务字段为空
**解决方案**:
- 检查任务数据是否正确保存到字典中
- 确认任务数据的映射逻辑是否正确

### 问题5: GUID格式错误
**症状**: processRouteId或processStepId不是有效的GUID格式
**解决方案**:
- 检查GUID解析逻辑
- 确保使用有效的GUID字符串

## 调试技巧

### 1. 查看详细日志
调试日志会显示每个字段的具体值：
```
[时间] 验证任务数据: 任务名称
[时间] TaskNumber: 任务编号
[时间] TaskName: 任务名称
[时间] ProductionOrderId: 生产工单ID
...
```

### 2. 检查JSON序列化
在HttpService中会打印完整的JSON请求数据，检查：
- `dto.tasks`数组是否包含任务对象
- 每个任务对象的字段是否都有正确的值
- 是否有null值或空字符串

### 3. 逐步验证
1. 先使用"🔍 字段检查"确认数据完整性
2. 再使用"📊 任务统计"查看任务数量
3. 最后使用"✅ 确定排产"进行实际调用

## 修复建议

### 1. 数据初始化
确保在构造函数中正确初始化所有必需的数据：
```csharp
public ScheduleProductionDialogViewModel(ProductionOrderModel order)
{
    _originalOrder = order;
    // 确保基础信息正确填充
    FillBasicInfo();
    // 确保工序数据正确加载
    _ = LoadProcessStepsAsync();
    // 确保工艺路线信息正确加载
    _ = LoadProcessRoutingAsync();
}
```

### 2. 空值处理
为所有可能为空的字段提供默认值：
```csharp
TaskNumber = string.IsNullOrEmpty(task.TaskId) ? $"TASK_{processStep.Id}_{DateTime.Now:yyyyMMddHHmmss}" : task.TaskId,
TaskName = string.IsNullOrEmpty(task.TaskName) ? $"{processStep.ProcessStepName}任务" : task.TaskName,
StationName = string.IsNullOrEmpty(task.StationName) ? "默认工位" : task.StationName,
```

### 3. GUID处理
确保GUID字段始终有有效值：
```csharp
var processRouteId = Guid.TryParse(ProcessRouteNumber, out var routeGuid) ? routeGuid : Guid.NewGuid();
var processStepId = Guid.TryParse(processStep.Id, out var stepGuid) ? stepGuid : Guid.NewGuid();
```

## 测试流程

### 推荐的测试顺序
1. **字段检查** - 使用"🔍 字段检查"按钮
2. **任务统计** - 使用"📊 任务统计"按钮
3. **直接测试** - 使用"🔧 直接测试"按钮
4. **API测试** - 使用"🧪 测试API"按钮
5. **确定排产** - 使用"✅ 确定排产"按钮

### 预期结果
- 字段检查应该显示"✅ 所有数据字段都正常"
- 任务统计应该显示正确的任务数量
- 直接测试应该成功
- API测试应该成功
- 确定排产应该成功

## 总结

通过系统性的字段检查和调试，可以准确定位哪些字段缺失或格式不正确。关键是要：

1. **使用字段检查工具** - 快速识别问题字段
2. **查看详细日志** - 了解每个字段的具体值
3. **提供默认值** - 确保所有字段都有有效值
4. **验证数据类型** - 确保GUID等特殊类型正确
5. **逐步测试** - 从简单到复杂逐步验证

如果问题仍然存在，请提供字段检查结果和调试日志，这将帮助进一步定位问题。 