# 任务数据问题解决方案

## 问题描述

点击"确定排产"按钮时出现"工单任务列表不能为空"的错误，这表明后端API验证发现任务列表为空。

## 问题原因分析

### 1. 任务数据收集问题
- 工序列表可能为空
- 每个工序的任务数据可能为空
- 任务数据收集逻辑可能有问题

### 2. 数据初始化问题
- 任务字典`_tasksByProcessStep`可能没有正确初始化
- 工序数据可能没有正确加载
- 任务数据可能没有正确保存到字典中

### 3. 数据验证问题
- 任务数据可能通过了前端验证，但后端验证失败
- 某些必需字段可能缺失或格式不正确

## 解决方案

### 1. 自动创建默认任务
系统已经实现了自动创建默认任务的功能：

```csharp
if (allTasks.Count == 0)
{
    // 如果没有任务数据，创建默认任务
    foreach (var processStep in ProcessSteps)
    {
        var defaultTask = new WorkOrderTaskRequest
        {
            SequenceNumber = allTasks.Count + 1,
            TaskNumber = $"TASK_{processStep.Id}_{DateTime.Now:yyyyMMddHHmmss}",
            TaskName = $"{processStep.ProcessStepName}任务",
            ProductionOrderId = _originalOrder.Id,
            StationName = "默认工位",
            ProcessCode = processStep.ProcessStepName,
            ProcessName = processStep.ProcessStepName,
            ProcessFlow = ProcessRouteName,
            ProcessType = "默认类型",
            TaskColor = "#1890FF",
            PlanQuantity = PlanQuantity,
            PlanStartTime = PlanStartTime,
            PlanEndTime = PlanEndTime,
            PlanDuration = (decimal)(PlanEndTime - PlanStartTime).TotalHours,
            Status = "待开工",
            ProcessRouteId = Guid.TryParse(ProcessRouteNumber, out var routeGuid) ? routeGuid : null,
            ProcessStepId = Guid.TryParse(processStep.Id, out var stepGuid) ? stepGuid : null,
            Priority = 4,
            Remarks = "系统自动生成的默认任务"
        };
        
        allTasks.Add(defaultTask);
    }
}
```

### 2. 任务数据验证
添加了任务数据完整性验证：

```csharp
private bool ValidateTaskData(WorkOrderTaskRequest task)
{
    if (string.IsNullOrEmpty(task.TaskNumber))
    {
        System.Windows.MessageBox.Show("任务编号不能为空", "验证错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
        return false;
    }

    if (string.IsNullOrEmpty(task.TaskName))
    {
        System.Windows.MessageBox.Show("任务名称不能为空", "验证错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
        return false;
    }

    if (string.IsNullOrEmpty(task.ProductionOrderId))
    {
        System.Windows.MessageBox.Show("生产工单ID不能为空", "验证错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
        return false;
    }

    if (task.PlanQuantity <= 0)
    {
        System.Windows.MessageBox.Show("计划数量必须大于0", "验证错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
        return false;
    }

    if (task.PlanStartTime >= task.PlanEndTime)
    {
        System.Windows.MessageBox.Show("计划开始时间必须早于结束时间", "验证错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
        return false;
    }

    return true;
}
```

### 3. 任务数据统计
添加了任务数据统计功能：

```csharp
private void ShowTaskDataStatistics()
{
    var totalTasks = 0;
    var taskDetails = new List<string>();

    foreach (var processStep in ProcessSteps)
    {
        if (_tasksByProcessStep.ContainsKey(processStep.Id))
        {
            var tasks = _tasksByProcessStep[processStep.Id];
            totalTasks += tasks.Count;
            taskDetails.Add($"{processStep.ProcessStepName}: {tasks.Count} 个任务");
        }
        else
        {
            taskDetails.Add($"{processStep.ProcessStepName}: 0 个任务");
        }
    }

    var message = $"任务数据统计:\n总任务数: {totalTasks}\n\n详细统计:\n{string.Join("\n", taskDetails)}";
    System.Windows.MessageBox.Show(message, "任务数据统计", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
}
```

## 调试步骤

### 1. 查看任务统计
1. 打开排产对话框
2. 点击"📊 任务统计"按钮
3. 查看当前任务数据状态
4. 确认每个工序是否有任务数据

### 2. 检查调试日志
1. 打开Visual Studio
2. 运行应用程序
3. 打开"输出"窗口
4. 点击"✅ 确定排产"按钮
5. 查看调试日志输出

### 3. 手动添加任务
1. 选择工序
2. 点击"新增"按钮
3. 填写任务信息
4. 保存任务
5. 再次尝试排产

## 预防措施

### 1. 数据初始化
确保在加载工序数据时正确初始化任务字典：

```csharp
private async Task LoadProcessStepsAsync()
{
    try
    {
        var response = await _processService.GetProductionOrderProcessesAsync(_originalOrder.Id);
        
        if (response.IsSuc && response.Data?.Data != null)
        {
            ProcessSteps.Clear();
            
            foreach (var step in response.Data.Data)
            {
                ProcessSteps.Add(step);
                // 初始化任务字典
                if (!_tasksByProcessStep.ContainsKey(step.Id))
                {
                    _tasksByProcessStep[step.Id] = new ObservableCollection<ProcessTaskModel>();
                }
            }
            
            // 默认选中第一个工序
            if (ProcessSteps.Count > 0)
            {
                SelectedProcessStep = ProcessSteps.First();
            }
        }
    }
    catch (Exception ex)
    {
        // 处理异常
    }
}
```

### 2. 数据持久化
确保任务数据在工序切换时正确保存：

```csharp
private void SelectProcessStep(ProcessStepModel? processStep)
{
    if (processStep == null) return;

    // 保存当前工序的任务到字典中（在切换之前）
    if (SelectedProcessStep != null)
    {
        _tasksByProcessStep[SelectedProcessStep.Id] = new ObservableCollection<ProcessTaskModel>(Tasks);
    }

    // 更新选中状态
    foreach (var step in ProcessSteps)
    {
        step.IsSelected = (step == processStep);
    }
    
    SelectedProcessStep = processStep;

    // 加载目标工序的任务
    LoadTasksForProcessStep(processStep.Id);
}
```

## 常见问题

### Q1: 为什么任务列表为空？
A1: 可能的原因包括：
- 工序数据没有正确加载
- 任务数据没有正确保存到字典中
- 任务数据收集逻辑有问题

### Q2: 如何确保有任务数据？
A2: 可以：
- 使用"📊 任务统计"按钮查看当前状态
- 手动添加任务
- 系统会自动创建默认任务

### Q3: 默认任务是否符合要求？
A3: 默认任务包含所有必需字段，但可能需要根据实际业务需求调整：
- 任务名称和编号
- 工位信息
- 时间安排
- 数量设置

## 总结

通过以上解决方案，可以有效解决"工单任务列表不能为空"的问题：

1. **自动创建默认任务** - 确保始终有任务数据
2. **任务数据验证** - 确保任务数据完整性
3. **任务数据统计** - 帮助诊断问题
4. **调试日志** - 提供详细的执行信息

如果问题仍然存在，请检查调试日志中的具体信息，这将帮助进一步定位问题。 