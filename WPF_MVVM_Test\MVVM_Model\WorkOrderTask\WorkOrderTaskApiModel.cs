using System;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.WorkOrderTask
{
    /// <summary>
    /// 工单任务API数据模型 - 对应后端API返回的数据结构
    /// </summary>
    public class WorkOrderTaskApiModel
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [JsonPropertyName("sequenceNumber")]
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        [JsonPropertyName("taskNumber")]
        public string TaskNumber { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        [JsonPropertyName("taskName")]
        public string TaskName { get; set; }

        /// <summary>
        /// 关联的生产工单ID
        /// </summary>
        [JsonPropertyName("productionOrderId")]
        public string ProductionOrderId { get; set; }

        /// <summary>
        /// 生产工单编号
        /// </summary>
        [JsonPropertyName("productionOrderNumber")]
        public string ProductionOrderNumber { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [JsonPropertyName("stationName")]
        public string StationName { get; set; }

        /// <summary>
        /// 工艺编号
        /// </summary>
        [JsonPropertyName("processCode")]
        public string ProcessCode { get; set; }

        /// <summary>
        /// 工艺名称
        /// </summary>
        [JsonPropertyName("processName")]
        public string ProcessName { get; set; }

        /// <summary>
        /// 工艺流程
        /// </summary>
        [JsonPropertyName("processFlow")]
        public string ProcessFlow { get; set; }

        /// <summary>
        /// 工艺类型
        /// </summary>
        [JsonPropertyName("processType")]
        public string ProcessType { get; set; }

        /// <summary>
        /// 任务颜色
        /// </summary>
        [JsonPropertyName("taskColor")]
        public string TaskColor { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        [JsonPropertyName("planQuantity")]
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际生产数量
        /// </summary>
        [JsonPropertyName("actualQuantity")]
        public decimal ActualQuantity { get; set; }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        [JsonPropertyName("planStartTime")]
        public string PlanStartTime { get; set; }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        [JsonPropertyName("planEndTime")]
        public string PlanEndTime { get; set; }

        /// <summary>
        /// 实际开工时间
        /// </summary>
        [JsonPropertyName("actualStartTime")]
        public string? ActualStartTime { get; set; }

        /// <summary>
        /// 实际完工时间
        /// </summary>
        [JsonPropertyName("actualEndTime")]
        public string? ActualEndTime { get; set; }

        /// <summary>
        /// 计划用时（小时）
        /// </summary>
        [JsonPropertyName("planDuration")]
        public decimal PlanDuration { get; set; }

        /// <summary>
        /// 实际用时（小时）
        /// </summary>
        [JsonPropertyName("actualDuration")]
        public decimal? ActualDuration { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; }

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        [JsonPropertyName("processRouteId")]
        public string? ProcessRouteId { get; set; }

        /// <summary>
        /// 工序ID
        /// </summary>
        [JsonPropertyName("processStepId")]
        public string? ProcessStepId { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        [JsonPropertyName("priority")]
        public int Priority { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remarks")]
        public string? Remarks { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonPropertyName("createTime")]
        public string CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [JsonPropertyName("updateTime")]
        public string UpdateTime { get; set; }
    }

    /// <summary>
    /// 报工请求模型
    /// </summary>
    public class WorkReportRequest
    {
        /// <summary>
        /// 工单任务ID
        /// </summary>
        [JsonPropertyName("workOrderTaskId")]
        public string WorkOrderTaskId { get; set; } = string.Empty;

        /// <summary>
        /// 检验代码
        /// </summary>
        [JsonPropertyName("inspectionCode")]
        public string InspectionCode { get; set; } = string.Empty;

        /// <summary>
        /// 检验项名称
        /// </summary>
        [JsonPropertyName("inspectionName")]
        public string InspectionName { get; set; } = string.Empty;

        /// <summary>
        /// 检验类型
        /// </summary>
        [JsonPropertyName("inspectionType")]
        public string InspectionType { get; set; } = string.Empty;

        /// <summary>
        /// 产品ID
        /// </summary>
        [JsonPropertyName("productId")]
        public string ProductId { get; set; } = "3fa85f64-5717-4562-b3fc-2c963f66afa6";

        /// <summary>
        /// 工艺步骤ID
        /// </summary>
        [JsonPropertyName("processStepId")]
        public string ProcessStepId { get; set; } = "3fa85f64-5717-4562-b3fc-2c963f66afa6";

        /// <summary>
        /// 站点ID
        /// </summary>
        [JsonPropertyName("stationId")]
        public string StationId { get; set; } = "3fa85f64-5717-4562-b3fc-2c963f66afa6";

        /// <summary>
        /// 班组ID
        /// </summary>
        [JsonPropertyName("teamId")]
        public string TeamId { get; set; } = "3fa85f64-5717-4562-b3fc-2c963f66afa6";

        /// <summary>
        /// 报告人ID
        /// </summary>
        [JsonPropertyName("reporterId")]
        public string ReporterId { get; set; } = "3fa85f64-5717-4562-b3fc-2c963f66afa6";

        /// <summary>
        /// 检验员ID
        /// </summary>
        [JsonPropertyName("inspectorId")]
        public string InspectorId { get; set; } = "3fa85f64-5717-4562-b3fc-2c963f66afa6";

        /// <summary>
        /// 报告数量
        /// </summary>
        [JsonPropertyName("reportedQuantity")]
        public int ReportedQuantity { get; set; } = 1;

        /// <summary>
        /// 报告时间
        /// </summary>
        [JsonPropertyName("reportTime")]
        public string ReportTime { get; set; } = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

        /// <summary>
        /// 检验时间
        /// </summary>
        [JsonPropertyName("inspectionTime")]
        public string InspectionTime { get; set; } = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

        /// <summary>
        /// 检验部门
        /// </summary>
        [JsonPropertyName("inspectionDepartment")]
        public string InspectionDepartment { get; set; } = string.Empty;

        /// <summary>
        /// 测试数量
        /// </summary>
        [JsonPropertyName("testedQuantity")]
        public int TestedQuantity { get; set; } = 0;

        /// <summary>
        /// 合格数量
        /// </summary>
        [JsonPropertyName("qualifiedQuantity")]
        public int QualifiedQuantity { get; set; } = 0;

        /// <summary>
        /// 不合格数量
        /// </summary>
        [JsonPropertyName("unqualifiedQuantity")]
        public int UnqualifiedQuantity { get; set; } = 0;

        /// <summary>
        /// 总体结果
        /// </summary>
        [JsonPropertyName("overallResult")]
        public string OverallResult { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark { get; set; } = string.Empty;
    }
}
