# 🏭 工艺流程功能说明

## ✨ 功能概述

在生产工单页面，用户可以点击"⚙️ 工艺流程"按钮查看指定工单的工艺流程信息。系统会弹出一个工艺流程对话框，显示工序导航和任务信息管理界面。

## 🎯 功能特点

### 📋 头部信息展示
- **工艺路线名称**：显示工艺路线的名称
- **工艺路线编号**：显示工艺路线的编号
- **标题**：显示"工艺流程"标题，带有通知徽章

### ⚙️ 工序导航
- **动态工序列表**：从后端API获取工序名称
- **工序切换**：点击不同工序可以切换查看对应的任务信息
- **选中状态**：当前选中的工序会高亮显示，带有蓝色下划线
- **箭头分隔**：工序之间用箭头分隔，表示流程顺序

### 📊 任务信息管理
- **任务列表**：显示当前选中工序的所有任务
- **任务详情**：包含序号、任务编号、任务名称、站点信息、计划数量、时间等
- **任务颜色**：每个任务有对应的颜色标识
- **操作功能**：支持新增、编辑、删除任务，查看物料信息

## 🔧 技术实现

### 文件结构
```
WPF_MVVM_Test/
├── MVVM_Model/Process/
│   ├── ProcessStepModel.cs          # 工序模型
│   └── ProcessTaskModel.cs          # 任务模型
├── MVVM_View/Process_process/
│   └── ProcessFlowControl.xaml      # 工艺流程界面
├── MVVM_ViewModel/Process_process/
│   └── ProcessFlowViewModel.cs      # 工艺流程ViewModel
├── MVVM_Services/
│   └── ProcessService.cs            # 工艺流程服务
└── MVVM_View/Converters/
    └── BooleanToStyleConverter.cs   # 样式转换器
```

### 核心类说明

#### 1. ProcessFlowViewModel
- **职责**：管理工艺流程的数据和业务逻辑
- **主要功能**：
  - 工序数据加载：从API获取工序列表
  - 任务数据加载：根据选中的工序加载任务信息
  - 工序切换：处理工序选择逻辑
  - 任务操作：新增、编辑、删除任务

#### 2. ProcessFlowControl.xaml
- **职责**：工艺流程的用户界面
- **设计特点**：
  - 现代化布局：清晰的头部、导航、操作、表格区域
  - 工序导航：水平排列的工序按钮，支持选中状态
  - 任务表格：完整的数据表格，支持各种操作
  - 响应式设计：适配不同屏幕尺寸

#### 3. ProcessService
- **API方法**：
  - `GetProductionOrderProcessesAsync`：获取工序列表
  - `GetProcessStepTasksAsync`：获取任务信息
  - `AddTaskAsync`：新增任务
  - `EditTaskAsync`：编辑任务
  - `DeleteTaskAsync`：删除任务

## 🚀 使用流程

### 1. 打开工艺流程
1. 在生产工单列表中找到目标工单
2. 点击操作列中的"⚙️ 工艺流程"按钮
3. 系统弹出工艺流程对话框

### 2. 查看工序信息
- 对话框顶部显示工艺路线的基本信息
- 工序导航区域显示所有工序，当前选中的工序会高亮显示
- 点击不同工序可以切换查看对应的任务信息

### 3. 管理任务信息
- 任务表格显示当前工序的所有任务
- 支持新增、编辑、删除任务操作
- 可以查看每个任务的物料信息

### 4. 数据交互
- 系统会自动从后端API获取工序和任务数据
- 如果API不可用，会使用测试数据进行演示
- 所有操作都有相应的加载状态和错误处理

## 🎨 界面设计

### 视觉特点
- **现代化设计**：使用Material Design风格
- **清晰布局**：头部信息、工序导航、操作按钮、数据表格分区明确
- **状态反馈**：选中状态、悬停效果、加载指示器
- **颜色系统**：统一的色彩方案，任务颜色标识

### 交互体验
- **流畅切换**：工序切换时任务数据自动更新
- **操作反馈**：按钮悬停、点击效果
- **加载状态**：数据加载时显示进度指示器
- **错误处理**：网络异常时使用测试数据，保证功能可用

## 🔗 API接口

### 获取工序列表
```
GET /api/ProductionOrder/GetProductionOrderProcesses/processes/{productionOrderId}?includeDeleted=true
```

### 获取任务信息
```
GET /api/Process/GetProcessStepTasks?processStepId={processStepId}&productionOrderId={productionOrderId}
```

### 示例响应数据
```json
{
  "data": [
    {
      "id": "ebcb19b6-c58e-43dd-a67e-6d3c3b471196",
      "processStepName": "原料预处理"
    },
    {
      "id": "b624d91e-99e2-47ed-832f-c07ef55fac39",
      "processStepName": "主加工"
    }
  ],
  "isSuc": true,
  "code": 200,
  "msg": "操作成功"
}
```

## 🛠️ 开发亮点

### 1. 架构优势
- **MVVM模式**：清晰的职责分离
- **组件化设计**：可复用的工艺流程控件
- **服务层封装**：统一的API调用接口

### 2. 用户体验
- **响应式设计**：适配不同屏幕尺寸
- **状态管理**：完善的加载和错误状态
- **测试数据**：API不可用时保证功能演示

### 3. 扩展性
- **模块化设计**：易于添加新功能
- **配置化**：工序和任务字段可配置
- **主题支持**：支持自定义主题和样式 