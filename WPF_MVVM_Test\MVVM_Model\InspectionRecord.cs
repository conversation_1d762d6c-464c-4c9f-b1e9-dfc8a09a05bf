using System;
using System.ComponentModel;

namespace WPF_MVVM_Test.MVVM_Model
{
    public class InspectionRecord : INotifyPropertyChanged
    {
        public string Id { get; set; }
        public int SequenceNumber { get; set; }
        public string TaskNumber { get; set; }
        public string InspectionCode { get; set; }
        public string InspectionName { get; set; }
        public string InspectionType { get; set; }
        public string Status { get; set; }
        public string ProductId { get; set; }
        public string ProcessStepId { get; set; }
        public string ProcessName { get; set; }
        public string StationId { get; set; }
        public string TeamId { get; set; }
        public string ReporterId { get; set; }
        public string InspectorId { get; set; }
        public string InspectorName { get; set; }
        public long ReportedQuantity { get; set; }
        public DateTime ReportTime { get; set; }
        public DateTime InspectionTime { get; set; }
        public string InspectionDepartment { get; set; }
        public long TestedQuantity { get; set; }
        public long QualifiedQuantity { get; set; }
        public long UnqualifiedQuantity { get; set; }
        public string OverallResult { get; set; }
        public string Remark { get; set; }
        public DateTime PlanStartTime { get; set; }
        public string OrderNumber { get; set; }
        public string OrderName { get; set; }
        public string ProductionPlanId { get; set; }
        public string Specification { get; set; }
        public string Unit { get; set; }
        public decimal OrderPlanQuantity { get; set; }
        public long OrderActualQuantity { get; set; }
        public DateTime OrderPlanStartTime { get; set; }
        public DateTime OrderPlanEndTime { get; set; }
        public DateTime OrderActualStartTime { get; set; }
        public DateTime OrderActualEndTime { get; set; }
        public string OrderStatus { get; set; }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class InspectionRecordResponse
    {
        public InspectionRecordData Data { get; set; }
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; }
    }

    public class InspectionRecordData
    {
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
        public InspectionRecord[] Data { get; set; }
    }
}