using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace WPF_MVVM_Test.MVVM_View.Converters
{
    /// <summary>
    /// 布尔值到背景色的转换器
    /// </summary>
    public class BooleanToBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return isSelected ? new SolidColorBrush(Color.FromRgb(24, 144, 255)) : Brushes.Transparent;
            }
            
            return Brushes.Transparent;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 