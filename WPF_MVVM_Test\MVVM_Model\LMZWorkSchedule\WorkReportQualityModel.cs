using System;
using System.Collections.Generic;

namespace WPF_MVVM_Test.MVVM_Model.LMZWorkSchedule
{
    /// <summary>
    /// 报工质检数据项模型
    /// </summary>
    public class WorkReportQualityItem
    {
        public Guid? Id { get; set; }
        public int? SequenceNumber { get; set; }
        public string? TaskNumber { get; set; }
        public string? InspectionCode { get; set; }
        public string? InspectionName { get; set; }
        public string? InspectionType { get; set; }
        public string? Status { get; set; }
        public string? ProductId { get; set; }
        public string? ProcessStepId { get; set; }
        public string? ProcessName { get; set; }
        public string? StationId { get; set; }
        public string? TeamId { get; set; }
        public string? ReporterId { get; set; }
        public string? InspectorId { get; set; }
        public string? InspectorName { get; set; }
        public int? ReportedQuantity { get; set; }
        public DateTime? ReportTime { get; set; }
        public DateTime? InspectionTime { get; set; }
        public string? InspectionDepartment { get; set; }
        public int? TestedQuantity { get; set; }
        public int? QualifiedQuantity { get; set; }
        public int? UnqualifiedQuantity { get; set; }
        public string? OverallResult { get; set; }
        public string? Remark { get; set; }
        public DateTime? PlanStartTime { get; set; }
        public string? OrderNumber { get; set; }
        public string? OrderName { get; set; }
        public string? ProductionPlanId { get; set; }
        public string? Specification { get; set; }
        public string? Unit { get; set; }
        public decimal? OrderPlanQuantity { get; set; }
        public decimal? OrderActualQuantity { get; set; }
        public DateTime? OrderPlanStartTime { get; set; }
        public DateTime? OrderPlanEndTime { get; set; }
        public DateTime? OrderActualStartTime { get; set; }
        public DateTime? OrderActualEndTime { get; set; }
        public string? OrderStatus { get; set; }
    }

    /// <summary>
    /// 报工质检分页数据模型
    /// </summary>
    public class WorkReportQualityPagedData
    {
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
        public List<WorkReportQualityItem> Data { get; set; } = new List<WorkReportQualityItem>();
    }

    /// <summary>
    /// 报工质检API响应模型
    /// </summary>
    public class WorkReportQualityApiResponse
    {
        public WorkReportQualityPagedData? Data { get; set; }
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string? Msg { get; set; }
    }

    /// <summary>
    /// 报工质检查询参数模型
    /// </summary>
    public class WorkReportQualityQueryParams
    {
        public string? OrderName { get; set; }
        public string? Status { get; set; }
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// 质检处理结果模型
    /// </summary>
    public class QualityInspectionResult
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public object? Data { get; set; }
    }

    /// <summary>
    /// 质检处理API响应模型
    /// </summary>
    public class QualityInspectionApiResponse
    {
        public object? Data { get; set; }
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string? Msg { get; set; }
    }
}