using System.Windows.Controls;
using WPF_MVVM_Test.MVVM_ViewModel.LMZWorkSchedule;

namespace WPF_MVVM_Test.MVVM_View.LMZWorkSchedule
{
    public partial class InspectionRecords : System.Windows.Controls.UserControl
    {
        private InspectionRecordsViewModel _viewModel;

        public InspectionRecords()
        {
            InitializeComponent();
            _viewModel = new InspectionRecordsViewModel();
            DataContext = _viewModel;
        }

        private void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_viewModel != null && sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                if (int.TryParse(selectedItem.Content.ToString(), out int pageSize))
                {
                    _viewModel.PageSize = pageSize;
                }
            }
        }
    }
}