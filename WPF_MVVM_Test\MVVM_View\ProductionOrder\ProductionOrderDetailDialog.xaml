<Window x:Class="WPF_MVVM_Test.MVVM_View.ProductionOrder.ProductionOrderDetailDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.ProductionOrder"
        Title="生产工单详情" Height="800" Width="1200" MinHeight="700" MinWidth="1000"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize">
    
    <Window.Resources>
        <!-- 进度条样式 -->
        <Style x:Key="ProgressBarStyle" TargetType="Border">
            <Setter Property="Height" Value="8"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Margin" Value="0,2"/>
        </Style>
        
        <!-- 选项卡按钮样式 -->
        <Style x:Key="TabButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <!-- 头部区域 -->
        <Border Background="White" Height="180" VerticalAlignment="Top" Padding="30,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题行 -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="{Binding OrderName}" FontSize="24" FontWeight="Bold" Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding OrderNumber}" FontSize="16" Foreground="#666" VerticalAlignment="Bottom"/>
                    </StackPanel>
                    
                    <!-- 状态标签 -->
                    <Border Background="#E6F7FF" BorderBrush="#1890FF" BorderThickness="2" 
                            CornerRadius="20" Padding="15,5" HorizontalAlignment="Right">
                        <TextBlock Text="{Binding Status}" Foreground="#1890FF" FontWeight="Bold"/>
                    </Border>
                </Grid>

                <!-- 创建信息 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
                    <TextBlock Text="创建人：" Foreground="#666"/>
                    <TextBlock Text="{Binding Creator}" Margin="0,0,30,0"/>
                    <TextBlock Text="创建时间：" Foreground="#666"/>
                    <TextBlock Text="{Binding CreateTime}"/>
                </StackPanel>

                <!-- 工单进度 -->
                <Grid Grid.Row="2" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="工单进度：" FontWeight="Bold" VerticalAlignment="Center"/>
                    
                    <!-- 进度条区域 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                        <Border Style="{StaticResource ProgressBarStyle}" Background="#52C41A" Width="80">
                            <TextBlock Text="0%" HorizontalAlignment="Center" Foreground="White" FontSize="10"/>
                        </Border>
                        <Border Style="{StaticResource ProgressBarStyle}" Background="#722ED1" Width="80" Margin="5,0">
                            <TextBlock Text="0%" HorizontalAlignment="Center" Foreground="White" FontSize="10"/>
                        </Border>
                        <Border Style="{StaticResource ProgressBarStyle}" Background="#1890FF" Width="80" Margin="5,0">
                            <TextBlock Text="0%" HorizontalAlignment="Center" Foreground="White" FontSize="10"/>
                        </Border>
                        <Border Style="{StaticResource ProgressBarStyle}" Background="#FA8C16" Width="80" Margin="5,0">
                            <TextBlock Text="0%" HorizontalAlignment="Center" Foreground="White" FontSize="10"/>
                        </Border>
                    </StackPanel>

                    <TextBlock Grid.Column="2" Text="查看详情" Foreground="#1890FF" Cursor="Hand" VerticalAlignment="Center"/>
                </Grid>

                <!-- 进度标签 -->
                <StackPanel Grid.Row="3" Orientation="Horizontal">
                    <TextBlock Text="开始" Margin="0,0,60,0" FontSize="12"/>
                    <TextBlock Text="完成" Margin="0,0,60,0" FontSize="12"/>
                    <TextBlock Text="关闭" Margin="0,0,60,0" FontSize="12"/>
                    <TextBlock Text="打印" FontSize="12"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 选项卡区域 -->
        <Border Background="White" Margin="0,180,0,0" BorderBrush="#E8E8E8" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 选项卡按钮 -->
                <ScrollViewer Grid.Row="0" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Hidden">
                    <ItemsControl ItemsSource="{Binding TabItems}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Button Content="{Binding}" Style="{StaticResource TabButtonStyle}"
                                        Command="{Binding DataContext.SelectTabCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                        CommandParameter="{Binding}"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- 内容区域 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
                    <ContentPresenter x:Name="TabContent"/>
                </ScrollViewer>
            </Grid>
        </Border>
    </Grid>
</Window>

