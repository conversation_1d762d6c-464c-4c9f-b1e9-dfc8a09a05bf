using System;
using System.Windows;
using System.Windows.Controls;
using WPF_MVVM_Test.MVVM_ViewModel.ProductionOrder;
using WPF_MVVM_Test.MVVM_Model.ProductionOrder;

namespace WPF_MVVM_Test.MVVM_View.ProductionOrder
{
    public partial class ProductionOrderDetailDialog : Window
    {
        private ProductionOrderDetailViewModel _viewModel;

        public ProductionOrderDetailDialog(ProductionOrderModel order)
        {
            InitializeComponent();
            
            _viewModel = new ProductionOrderDetailViewModel(order);
            DataContext = _viewModel;
            
            // 订阅选项卡切换事件
            _viewModel.PropertyChanged += ViewModel_PropertyChanged;
            
            // 初始显示基础信息
            ShowBasicInfo();
        }

        private void ViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(_viewModel.SelectedTab))
            {
                ShowTabContent();
            }
        }

        private void ShowTabContent()
        {
            switch (_viewModel.SelectedTab)
            {
                case "基础信息":
                    ShowBasicInfo();
                    break;
                default:
                    ShowComingSoon();
                    break;
            }
        }

        private void ShowBasicInfo()
        {
            var basicInfoGrid = CreateBasicInfoGrid();
            TabContent.Content = basicInfoGrid;
        }

        private void ShowComingSoon()
        {
            var textBlock = new TextBlock
            {
                Text = "功能开发中...",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = System.Windows.Media.Brushes.Gray
            };
            TabContent.Content = textBlock;
        }

        private Grid CreateBasicInfoGrid()
        {
            var grid = new Grid();
            
            // 添加列定义
            grid.ColumnDefinitions.Add(new ColumnDefinition());
            grid.ColumnDefinitions.Add(new ColumnDefinition());
            
            // 添加行定义
            for (int i = 0; i < 11; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            }

            // 添加基础信息字段
            AddInfoField(grid, 0, 0, "工单编号", _viewModel.WorkOrderNumber);
            AddInfoField(grid, 1, 0, "工单名称", _viewModel.WorkOrderName);
            AddInfoField(grid, 2, 0, "关联计划", _viewModel.RelatedPlan);
            AddInfoField(grid, 3, 0, "计划编号", _viewModel.PlanNumber);
            AddInfoField(grid, 4, 0, "类型", _viewModel.OrderType);
            AddInfoField(grid, 5, 0, "订单编号", _viewModel.SalesOrderNumber);
            AddInfoField(grid, 6, 0, "产品名称", _viewModel.ProductName);
            AddInfoField(grid, 7, 0, "产品编号", _viewModel.ProductNumber);
            AddInfoField(grid, 8, 0, "规格型号", _viewModel.Specification);
            AddInfoField(grid, 9, 0, "产品类型", _viewModel.ProductType);
            AddInfoField(grid, 10, 0, "单位", _viewModel.Unit);

            return grid;
        }

        private void AddInfoField(Grid grid, int row, int column, string label, string value)
        {
            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 10, 0, 0)
            };

            var labelBlock = new TextBlock
            {
                Text = label,
                Width = 100,
                Foreground = System.Windows.Media.Brushes.Gray
            };

            var valueBlock = new TextBlock
            {
                Text = value ?? ""
            };

            stackPanel.Children.Add(labelBlock);
            stackPanel.Children.Add(valueBlock);

            Grid.SetRow(stackPanel, row);
            Grid.SetColumn(stackPanel, column);
            grid.Children.Add(stackPanel);
        }
    }
}

