using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace WPF_MVVM_Test.MVVM_View.Converters
{
    /// <summary>
    /// 布尔值到样式的转换器
    /// </summary>
    public class BooleanToStyleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected && parameter is string styleNames)
            {
                var names = styleNames.Split('|');
                if (names.Length == 2)
                {
                    var selectedStyleName = names[0];
                    var unselectedStyleName = names[1];
                    
                    // 尝试从应用程序资源中获取样式
                    var styleName = isSelected ? selectedStyleName : unselectedStyleName;
                    
                    // 查找样式资源
                    if (Application.Current.Resources.Contains(styleName))
                    {
                        return Application.Current.Resources[styleName];
                    }
                }
            }
            
            return null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 