﻿using System.Configuration;
using System.Data;
using System.Windows;
using System.Threading.Tasks;

namespace WPF_MVVM_Test
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override async void OnStartup(StartupEventArgs e)
        {
            // 在应用启动时测试API连接
            await TestApiConnection.RunAllTestsAsync();

            base.OnStartup(e);
        }
    }

}
