using System;
using System.Threading.Tasks;
using System.Text.Json;
using WPF_MVVM_Test.MVVM_Model.Common;
using WPF_MVVM_Test.MVVM_Model.Process;
using System.Net.Http;

namespace WPF_MVVM_Test.MVVM_Services
{
    /// <summary>
    /// 工艺流程服务
    /// </summary>
    public class ProcessService
    {
        private readonly HttpService _httpService;

        public ProcessService()
        {
            _httpService = new HttpService();
        }

        /// <summary>
        /// 获取生产工单的工序列表
        /// </summary>
        /// <param name="productionOrderId">生产工单ID</param>
        /// <param name="includeDeleted">是否包含已删除的工序</param>
        /// <returns></returns>
        public async Task<ApiResponse<ProcessStepApiResponse>> GetProductionOrderProcessesAsync(
            string productionOrderId, 
            bool includeDeleted = true)
        {
            try
            {
                var url = $"api/ProductionOrder/GetProductionOrderProcesses/processes/{productionOrderId}?includeDeleted={includeDeleted}";
                var response = await _httpService.GetAsync<ProcessStepApiResponse>(url);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<ProcessStepApiResponse>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"获取工序列表失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取指定工序的任务信息
        /// </summary>
        /// <param name="processStepId">工序ID</param>
        /// <param name="productionOrderId">生产工单ID</param>
        /// <returns></returns>
        public async Task<ApiResponse<ProcessTaskApiResponse>> GetProcessStepTasksAsync(
            string processStepId, 
            string productionOrderId)
        {
            try
            {
                var url = $"api/Process/GetProcessStepTasks?processStepId={processStepId}&productionOrderId={productionOrderId}";
                var response = await _httpService.GetAsync<ProcessTaskApiResponse>(url);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<ProcessTaskApiResponse>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"获取任务信息失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 新增任务
        /// </summary>
        /// <param name="taskData">任务数据</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> AddTaskAsync(object taskData)
        {
            try
            {
                var response = await _httpService.PostAsync<object>("api/Process/AddTask", taskData);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"新增任务失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 批量添加任务
        /// </summary>
        /// <param name="batchTaskData">批量任务数据</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> BatchAddWorkOrderTasksAsync(object batchTaskData)
        {
            try
            {
                // 使用与curl命令完全匹配的URL
                var url = "api/ProductionOrder/BatchAddWorkOrderTasks/work-order-tasks/batch";
                
                // 添加调试日志
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 开始批量添加任务");
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] URL: {url}");
                
                // 序列化请求数据用于调试
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                };
                var requestJson = JsonSerializer.Serialize(batchTaskData, jsonOptions);
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 请求数据: {requestJson}");
                
                var response = await _httpService.PostAsync<object>(url, batchTaskData);
                
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 响应结果: IsSuc={response.IsSuc}, Code={response.Code}, Msg={response.Msg}");
                
                return response;
            }
            catch (HttpRequestException ex)
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] HTTP请求异常: {ex.Message}");
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"HTTP请求错误: {ex.Message}"
                };
            }
            catch (JsonException ex)
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] JSON序列化异常: {ex.Message}");
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"JSON序列化错误: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 批量添加任务异常: {ex.Message}");
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"批量添加任务失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 编辑任务
        /// </summary>
        /// <param name="taskData">任务数据</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> EditTaskAsync(object taskData)
        {
            try
            {
                var response = await _httpService.PutAsync<object>("api/Process/EditTask", taskData);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"编辑任务失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> DeleteTaskAsync(string taskId)
        {
            try
            {
                var url = $"api/Process/DeleteTask/{taskId}";
                var response = await _httpService.DeleteAsync<object>(url);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"删除任务失败: {ex.Message}"
                };
            }
        }

        public void Dispose()
        {
            _httpService?.Dispose();
        }
    }
} 