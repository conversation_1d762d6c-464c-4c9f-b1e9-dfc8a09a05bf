using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model.Common;
using WPF_MVVM_Test.MVVM_Model.ProductionOrder;
using WPF_MVVM_Test.MVVM_Services;

namespace WPF_MVVM_Test.MVVM_ViewModel.ProductionOrder
{
    public class ProductionOrderDetailViewModel : BaseViewModel
    {
        private ProductionOrderModel _orderDetail;
        
        public ProductionOrderDetailViewModel(ProductionOrderModel order)
        {
            _orderDetail = order;
            InitializeData();
            InitializeCommands();
        }

        #region 属性

        public string OrderNumber => _orderDetail?.OrderNumber ?? "";
        public string OrderName => _orderDetail?.ProductName ?? "";
        public string Creator => "李小明";
        public string CreateTime => "2025-08-01 10:21:49";
        public string Status => _orderDetail?.Status ?? "进行中";

        // 进度信息
        public int StartProgress => 0;
        public int CompleteProgress => 0;
        public int CloseProgress => 0;
        public int PrintProgress => 0;

        // 基础信息
        public string WorkOrderNumber => _orderDetail?.OrderNumber ?? "";
        public string WorkOrderName => _orderDetail?.ProductName ?? "";
        public string RelatedPlan => "备货品生产计划";
        public string PlanNumber => "SCJH023515";
        public string OrderType => "销售订单";
        public string SalesOrderNumber => "XSDDBH021351";
        public string ProductName => _orderDetail?.ProductName ?? "";
        public string ProductNumber => "XCP2315";
        public string Specification => "红色";
        public string ProductType => "半成品";
        public string Unit => "个";
        public string BomCode => "BOM000036";
        public string PlanQuantity => _orderDetail?.PlanQuantity.ToString() ?? "200";
        public string ActualQuantity => "";
        public string PlanStartTime => "2023-04-19";
        public string PlanEndTime => "2023-06-19";
        public string ActualStartTime => "2023-04-19 20:04:48";
        public string ActualEndTime => "";
        public string ProductionBatch => "";
        public string RequiredDate => "2023-04-19";

        // 选项卡
        private string _selectedTab = "基础信息";
        public string SelectedTab
        {
            get => _selectedTab;
            set => SetProperty(ref _selectedTab, value);
        }

        public ObservableCollection<string> TabItems { get; } = new ObservableCollection<string>
        {
            "基础信息", "物料清单", "领料记录", "工序任务", "报工记录", "质检记录", "退料记录", "入库记录", "执行进度"
        };

        #endregion

        #region 命令

        public ICommand SelectTabCommand { get; private set; }
        public ICommand CloseCommand { get; private set; }

        #endregion

        #region 初始化

        private void InitializeData()
        {
            // 初始化数据
        }

        private void InitializeCommands()
        {
            SelectTabCommand = new RelayCommand<string>(SelectTab);
            CloseCommand = new RelayCommand(Close);
        }

        #endregion

        #region 命令方法

        private void SelectTab(string tabName)
        {
            SelectedTab = tabName;
        }

        private void Close()
        {
            // 关闭详情页面的逻辑
        }

        #endregion
    }
}



