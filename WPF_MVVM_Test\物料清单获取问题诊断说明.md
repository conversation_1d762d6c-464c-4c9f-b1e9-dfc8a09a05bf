# 物料清单获取问题诊断说明

## 问题描述

用户反馈点击排产获取不到物料清单的信息，昨天还能获取到。

## 问题分析

经过检查，我没有修改过物料清单相关的代码。可能的问题包括：

1. **后端API问题** - 后端服务可能有问题
2. **网络连接问题** - 网络连接可能不稳定
3. **工单数据问题** - 工单ID或数据可能有问题
4. **API响应格式变化** - 后端API响应格式可能发生了变化

## 诊断步骤

### 1. 检查调试日志

我已经增强了物料清单加载的调试日志，现在会输出详细信息：

```
[时间] === 开始加载物料信息 ===
[时间] 工单ID: xxx
[时间] 工单编号: xxx
[时间] 工单名称: xxx
[时间] API响应结果:
[时间]   IsSuc: true/false
[时间]   Code: xxx
[时间]   Msg: xxx
[时间]   Data是否为null: true/false
[时间] 物料信息加载成功，物料数量: x
[时间] 处理物料: xxx (编号: xxx)
[时间] 物料信息处理完成，BomItems数量: x
[时间] === 物料信息加载结束 ===
```

### 2. 检查API调用

物料清单API调用代码：
```csharp
var url = $"http://localhost:64922/api/ProductionOrder/GetProductionOrderMaterialList/material-list/{productionOrderId}";
```

**注意**: URL中包含了`/material-list/`路径，这是必需的。

### 3. 检查响应模型

物料清单响应模型：
```csharp
public class ProductionOrderMaterialResponse
{
    public ProductionOrderMaterialData Data { get; set; }
    public bool IsSuc { get; set; }
    public int Code { get; set; }
    public string Msg { get; set; } = string.Empty;
}

public class ProductionOrderMaterialData
{
    public List<MaterialItem> Materials { get; set; } = new List<MaterialItem>();
    // 其他属性...
}
```

## 排查方法

### 1. 查看调试日志

1. 打开排产对话框
2. 查看Visual Studio输出窗口
3. 查找物料信息加载的日志
4. 确认API响应状态和错误信息

### 2. 检查后端服务

1. 确认后端服务是否正常运行
2. 检查API端点是否可访问
3. 验证工单ID是否有效

### 3. 测试API调用

可以使用curl命令测试API：
```bash
curl -X GET "http://localhost:64922/api/ProductionOrder/GetProductionOrderMaterialList/material-list/{工单ID}" \
  -H "accept: text/plain"
```

### 4. 检查网络连接

1. 确认网络连接正常
2. 检查防火墙设置
3. 验证端口64922是否可访问

## 可能的问题和解决方案

### 1. API返回失败

**症状**: `IsSuc: false`
**可能原因**: 
- 后端服务异常
- 工单ID不存在
- 权限问题

**解决方案**:
- 检查后端服务日志
- 验证工单ID
- 检查用户权限

### 2. 数据为空

**症状**: `Data是否为null: true` 或 `Materials数量: 0`
**可能原因**:
- 工单没有关联的物料清单
- 数据被删除或修改

**解决方案**:
- 检查工单的物料清单配置
- 重新配置物料清单

### 3. 网络连接问题

**症状**: 异常信息包含网络相关错误
**可能原因**:
- 网络连接中断
- 防火墙阻止
- 服务端口关闭

**解决方案**:
- 检查网络连接
- 配置防火墙规则
- 重启后端服务

### 4. 数据格式问题

**症状**: 数据解析失败
**可能原因**:
- API响应格式发生变化
- JSON序列化问题

**解决方案**:
- 检查API响应格式
- 更新响应模型

## 错误处理

如果API调用失败，系统会：

1. **显示错误消息** - 向用户显示具体的错误信息
2. **清空物料列表** - 清空BomItems集合，不显示任何数据
3. **记录调试日志** - 在输出窗口记录详细的错误信息

## 验证步骤

### 1. 基本验证
1. 打开排产对话框
2. 查看调试日志输出
3. 确认物料清单是否显示

### 2. 详细验证
1. 检查API响应状态
2. 确认数据解析是否成功
3. 验证物料数量是否正确

### 3. 功能验证
1. 确认物料清单界面正常显示
2. 验证物料信息是否正确
3. 测试排产功能是否正常

## 联系支持

如果问题持续存在，请提供以下信息：

1. **调试日志** - 完整的物料清单加载日志
2. **工单信息** - 工单ID、编号、名称
3. **错误信息** - 具体的错误消息
4. **环境信息** - 后端服务状态、网络环境

## 总结

物料清单获取问题可能由多种原因引起，通过详细的调试日志可以帮助快速定位问题。建议按以下顺序排查：

1. 查看调试日志确认问题类型
2. 检查后端服务状态
3. 验证网络连接
4. 测试API调用
5. 检查数据配置

如果问题仍然存在，请提供详细的调试信息以便进一步分析。 