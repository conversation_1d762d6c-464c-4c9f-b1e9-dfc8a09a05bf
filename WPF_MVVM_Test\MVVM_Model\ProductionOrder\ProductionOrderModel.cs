using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace WPF_MVVM_Test.MVVM_Model.ProductionOrder
{
    public class ProductionOrderModel : INotifyPropertyChanged
    {
        // 后端字段
        private string _id;
        private string _orderNumber;
        private string _orderName;
        private string _productionPlanId;
        private string _productionPlan;
        private string _productId;
        private string _productName;
        private string _productNumber;
        private string _specification;
        private string _unit;
        private decimal _planQuantity;
        private decimal _actualQuantity;
        private DateTime? _planStartTime;
        private DateTime? _planEndTime;
        private DateTime? _actualStartTime;
        private DateTime? _actualEndTime;
        private string _status;
        private string _bomCode = string.Empty;

        // UI相关字段
        private bool _isSelected;
        private int _index;
        private string _process1Progress;
        private string _process2Progress;
        private string _process3Progress;
        private string _process4Progress;

        // 后端字段属性
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value);
        }

        public string OrderName
        {
            get => _orderName;
            set => SetProperty(ref _orderName, value);
        }

        public string ProductionPlanId
        {
            get => _productionPlanId;
            set => SetProperty(ref _productionPlanId, value);
        }

        public string ProductionPlan
        {
            get => _productionPlan;
            set => SetProperty(ref _productionPlan, value);
        }

        public string ProductId
        {
            get => _productId;
            set => SetProperty(ref _productId, value);
        }

        public string ProductName
        {
            get => _productName;
            set => SetProperty(ref _productName, value);
        }

        public string ProductNumber
        {
            get => _productNumber;
            set => SetProperty(ref _productNumber, value);
        }

        public string Specification
        {
            get => _specification;
            set => SetProperty(ref _specification, value);
        }

        public string Unit
        {
            get => _unit;
            set => SetProperty(ref _unit, value);
        }

        public decimal PlanQuantity
        {
            get => _planQuantity;
            set => SetProperty(ref _planQuantity, value);
        }

        public decimal ActualQuantity
        {
            get => _actualQuantity;
            set => SetProperty(ref _actualQuantity, value);
        }

        public DateTime? PlanStartTime
        {
            get => _planStartTime;
            set => SetProperty(ref _planStartTime, value);
        }

        public DateTime? PlanEndTime
        {
            get => _planEndTime;
            set => SetProperty(ref _planEndTime, value);
        }

        public DateTime? ActualStartTime
        {
            get => _actualStartTime;
            set => SetProperty(ref _actualStartTime, value);
        }

        public DateTime? ActualEndTime
        {
            get => _actualEndTime;
            set => SetProperty(ref _actualEndTime, value);
        }

        public string Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(CanSchedule));
                    OnPropertyChanged(nameof(CanView));
                    SetProcessProgress();
                }
            }
        }

        public string BomCode
        {
            get => _bomCode;
            set => SetProperty(ref _bomCode, value);
        }

        // UI相关属性
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        public string Process1Progress
        {
            get => _process1Progress;
            set => SetProperty(ref _process1Progress, value);
        }

        public string Process2Progress
        {
            get => _process2Progress;
            set => SetProperty(ref _process2Progress, value);
        }

        public string Process3Progress
        {
            get => _process3Progress;
            set => SetProperty(ref _process3Progress, value);
        }

        public string Process4Progress
        {
            get => _process4Progress;
            set => SetProperty(ref _process4Progress, value);
        }

        // 计算属性
        public string StatusText => GetStatusText();
        public string StatusColor => GetStatusColor();
        public bool CanSchedule => Status == "待排产";
        public bool CanView => Status != "待排产";

        // 兼容性属性（用于UI绑定）
        public string AssociatedPlan => ProductionPlan ?? "无";
        public string ProductType => "标准产品"; // 可以根据实际需求调整
        public DateTime? DemandDate => PlanEndTime;
        public DateTime? StartTime => PlanStartTime;
        public DateTime? EndTime => PlanEndTime;

        private string GetStatusText()
        {
            return Status switch
            {
                "待排产" => "待排产",
                "未开始" => "未开始",
                "进行中" => "进行中",
                "已完成" => "已完成",
                "已暂停" => "已暂停",
                "已关闭" => "已关闭",
                _ => Status
            };
        }

        private string GetStatusColor()
        {
            return Status switch
            {
                "待排产" => "#8C8C8C", // 灰色
                "未开始" => "#1890FF", // 蓝色
                "进行中" => "#52C41A", // 绿色
                "已完成" => "#FA8C16", // 橙色
                "已暂停" => "#FF4D4F", // 红色
                "已关闭" => "#6C757D", // 深灰色
                _ => "#8C8C8C" // 默认灰色
            };
        }

        private void SetProcessProgress()
        {
            // 根据状态设置工序进度
            switch (Status)
            {
                case "待排产":
                    Process1Progress = "待开始";
                    Process2Progress = "待开始";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "未开始":
                    Process1Progress = "准备中";
                    Process2Progress = "待开始";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "进行中":
                    Process1Progress = "已完成";
                    Process2Progress = "进行中";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "已完成":
                    Process1Progress = "已完成";
                    Process2Progress = "已完成";
                    Process3Progress = "已完成";
                    Process4Progress = "已完成";
                    break;
                case "已暂停":
                    Process1Progress = "已完成";
                    Process2Progress = "已暂停";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
                case "已关闭":
                    Process1Progress = "已关闭";
                    Process2Progress = "已关闭";
                    Process3Progress = "已关闭";
                    Process4Progress = "已关闭";
                    break;
                default:
                    Process1Progress = "待开始";
                    Process2Progress = "待开始";
                    Process3Progress = "待开始";
                    Process4Progress = "待开始";
                    break;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
} 
