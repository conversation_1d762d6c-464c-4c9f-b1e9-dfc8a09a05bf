<UserControl x:Class="WPF_MVVM_Test.MVVM_View.LMZWorkSchedule.WorkReportQuality"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel.LMZWorkSchedule"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1200">

    <UserControl.DataContext>
        <vm:WorkReportQualityViewModel/>
    </UserControl.DataContext>



    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/><!-- 搜索区域 -->
            <RowDefinition Height="*"/><!-- 数据表格区域 -->
            <RowDefinition Height="Auto"/><!-- 分页区域 -->
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15">
            <StackPanel>
                <TextBlock Text="报工质检" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Margin="0,0,0,15"
                           Foreground="#2E3440"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 工单名称搜索 -->
                    <TextBox Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="工单名称"
                             Text="{Binding OrderNameFilter, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"
                             Height="60"/>

                    <!-- 状态搜索 -->
                    <ComboBox Grid.Column="1"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              materialDesign:HintAssist.Hint="状态"
                              Text="{Binding StatusFilter, UpdateSourceTrigger=PropertyChanged}"
                              IsEditable="True"
                              Margin="0,0,10,0"
                              Height="60">
                        <ComboBoxItem Content="待检验"/>
                        <ComboBoxItem Content="检验中"/>
                        <ComboBoxItem Content="已完成"/>
                        <ComboBoxItem Content="不合格"/>
                    </ComboBox>

                    <!-- 搜索按钮 -->
                    <Button Grid.Column="2"
                            Command="{Binding SearchCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="搜索"
                            Height="40"
                            Width="70"
                            Margin="0,0,10,0"/>

                    <!-- 刷新按钮 -->
                    <Button Grid.Column="3"
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="刷新"
                            Height="40"
                            Width="70"/>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- 数据表格区域 -->
        <materialDesign:Card Grid.Row="1" Margin="10,0,10,10">
            <Grid>
                <!-- 加载指示器 -->
                <ProgressBar IsIndeterminate="True" 
                             VerticalAlignment="Top"
                             Height="4">
                    <ProgressBar.Style>
                        <Style TargetType="ProgressBar">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ProgressBar.Style>
                </ProgressBar>

                <!-- 无数据提示 -->
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding IsLoading}" Value="False"/>
                                        <Condition Binding="{Binding WorkReportQualityItems.Count}" Value="0"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Visibility" Value="Visible"/>
                                </MultiDataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>

                    <materialDesign:PackIcon Kind="ClipboardCheckMultipleOutline" 
                                             Width="64" 
                                             Height="64" 
                                             Foreground="#CCCCCC"
                                             Margin="0,0,0,20"/>
                    <TextBlock Text="暂无报工质检数据" 
                               FontSize="16" 
                               Foreground="#999999"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="请尝试调整搜索条件或刷新数据" 
                               FontSize="12" 
                               Foreground="#CCCCCC"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                </StackPanel>

                <!-- 数据表格 -->
                <DataGrid ItemsSource="{Binding WorkReportQualityItems}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F8F9FA"
                          FontSize="12">

                    <DataGrid.Columns>
                        <!-- 序号 -->
                        <DataGridTextColumn Header="序号" 
                                            Binding="{Binding SequenceNumber}" 
                                            Width="60"/>

                        <!-- ID -->
                        <DataGridTextColumn Header="ID" 
                                            Binding="{Binding Id}" 
                                            Width="100"/>

                        <!-- 任务编号 -->
                        <DataGridTextColumn Header="任务编号" 
                                            Binding="{Binding TaskNumber}" 
                                            Width="120"/>

                        <!-- 检验代码 -->
                        <DataGridTextColumn Header="检验代码" 
                                            Binding="{Binding InspectionCode}" 
                                            Width="100"/>

                        <!-- 检验名称 -->
                        <DataGridTextColumn Header="检验名称" 
                                            Binding="{Binding InspectionName}" 
                                            Width="120"/>

                        <!-- 检验类型 -->
                        <DataGridTextColumn Header="检验类型" 
                                            Binding="{Binding InspectionType}" 
                                            Width="100"/>

                        <!-- 状态 -->
                        <DataGridTemplateColumn Header="状态" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10" Padding="8,2">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Setter Property="Background" Value="#E3F2FD"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="待检验">
                                                        <Setter Property="Background" Value="#FFF3E0"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="检验中">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="已完成">
                                                        <Setter Property="Background" Value="#E3F2FD"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="不合格">
                                                        <Setter Property="Background" Value="#FFEBEE"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding Status}" 
                                                   FontSize="10" 
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="#1976D2"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="待检验">
                                                            <Setter Property="Foreground" Value="#F57C00"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="检验中">
                                                            <Setter Property="Foreground" Value="#388E3C"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="已完成">
                                                            <Setter Property="Foreground" Value="#1976D2"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="不合格">
                                                            <Setter Property="Foreground" Value="#D32F2F"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 产品ID -->
                        <DataGridTextColumn Header="产品ID" 
                                            Binding="{Binding ProductId}" 
                                            Width="100"/>

                        <!-- 工序步骤ID -->
                        <DataGridTextColumn Header="工序步骤ID" 
                                            Binding="{Binding ProcessStepId}" 
                                            Width="100"/>

                        <!-- 工序名称 -->
                        <DataGridTextColumn Header="工序名称" 
                                            Binding="{Binding ProcessName}" 
                                            Width="100"/>

                        <!-- 工位ID -->
                        <DataGridTextColumn Header="工位ID" 
                                            Binding="{Binding StationId}" 
                                            Width="80"/>

                        <!-- 班组ID -->
                        <DataGridTextColumn Header="班组ID" 
                                            Binding="{Binding TeamId}" 
                                            Width="80"/>

                        <!-- 报工员ID -->
                        <DataGridTextColumn Header="报工员ID" 
                                            Binding="{Binding ReporterId}" 
                                            Width="100"/>

                        <!-- 检验员ID -->
                        <DataGridTextColumn Header="检验员ID" 
                                            Binding="{Binding InspectorId}" 
                                            Width="100"/>

                        <!-- 检验员 -->
                        <DataGridTextColumn Header="检验员" 
                                            Binding="{Binding InspectorName}" 
                                            Width="80"/>

                        <!-- 报工数量 -->
                        <DataGridTextColumn Header="报工数量" 
                                            Binding="{Binding ReportedQuantity}" 
                                            Width="80"/>

                        <!-- 报工时间 -->
                        <DataGridTextColumn Header="报工时间" 
                                            Binding="{Binding ReportTime, StringFormat=yyyy-MM-dd HH:mm}" 
                                            Width="130"/>

                        <!-- 检验时间 -->
                        <DataGridTextColumn Header="检验时间" 
                                            Binding="{Binding InspectionTime, StringFormat=yyyy-MM-dd HH:mm}" 
                                            Width="130"/>

                        <!-- 检验部门 -->
                        <DataGridTextColumn Header="检验部门" 
                                            Binding="{Binding InspectionDepartment}" 
                                            Width="100"/>

                        <!-- 检验数量 -->
                        <DataGridTextColumn Header="检验数量" 
                                            Binding="{Binding TestedQuantity}" 
                                            Width="80"/>

                        <!-- 合格数量 -->
                        <DataGridTextColumn Header="合格数量" 
                                            Binding="{Binding QualifiedQuantity}" 
                                            Width="80"/>

                        <!-- 不合格数量 -->
                        <DataGridTextColumn Header="不合格数量" 
                                            Binding="{Binding UnqualifiedQuantity}" 
                                            Width="90"/>

                        <!-- 总体结果 -->
                        <DataGridTextColumn Header="总体结果" 
                                            Binding="{Binding OverallResult}" 
                                            Width="80"/>

                        <!-- 计划开始时间 -->
                        <DataGridTextColumn Header="计划开始时间" 
                                            Binding="{Binding PlanStartTime, StringFormat=yyyy-MM-dd HH:mm}" 
                                            Width="130"/>

                        <!-- 工单编号 -->
                        <DataGridTextColumn Header="工单编号" 
                                            Binding="{Binding OrderNumber}" 
                                            Width="150"/>

                        <!-- 工单名称 -->
                        <DataGridTextColumn Header="工单名称" 
                                            Binding="{Binding OrderName}" 
                                            Width="200"/>

                        <!-- 生产计划ID -->
                        <DataGridTextColumn Header="生产计划ID" 
                                            Binding="{Binding ProductionPlanId}" 
                                            Width="120"/>

                        <!-- 规格 -->
                        <DataGridTextColumn Header="规格" 
                                            Binding="{Binding Specification}" 
                                            Width="120"/>

                        <!-- 单位 -->
                        <DataGridTextColumn Header="单位" 
                                            Binding="{Binding Unit}" 
                                            Width="60"/>

                        <!-- 工单计划数量 -->
                        <DataGridTextColumn Header="工单计划数量" 
                                            Binding="{Binding OrderPlanQuantity}" 
                                            Width="100"/>

                        <!-- 工单实际数量 -->
                        <DataGridTextColumn Header="工单实际数量" 
                                            Binding="{Binding OrderActualQuantity}" 
                                            Width="100"/>

                        <!-- 工单计划开始时间 -->
                        <DataGridTextColumn Header="工单计划开始时间" 
                                            Binding="{Binding OrderPlanStartTime, StringFormat=yyyy-MM-dd HH:mm}" 
                                            Width="140"/>

                        <!-- 工单计划结束时间 -->
                        <DataGridTextColumn Header="工单计划结束时间" 
                                            Binding="{Binding OrderPlanEndTime, StringFormat=yyyy-MM-dd HH:mm}" 
                                            Width="140"/>

                        <!-- 工单实际开始时间 -->
                        <DataGridTextColumn Header="工单实际开始时间" 
                                            Binding="{Binding OrderActualStartTime, StringFormat=yyyy-MM-dd HH:mm}" 
                                            Width="140"/>

                        <!-- 工单实际结束时间 -->
                        <DataGridTextColumn Header="工单实际结束时间" 
                                            Binding="{Binding OrderActualEndTime, StringFormat=yyyy-MM-dd HH:mm}" 
                                            Width="140"/>

                        <!-- 工单状态 -->
                        <DataGridTextColumn Header="工单状态" 
                                            Binding="{Binding OrderStatus}" 
                                            Width="80"/>

                        <!-- 备注 -->
                        <DataGridTextColumn Header="备注" 
                                            Binding="{Binding Remark}" 
                                            Width="200"/>

                        <!-- 操作列 -->
                        <DataGridTemplateColumn Header="操作" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                            Command="{Binding DataContext.ViewDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Content="查看详情"
                                            Height="30"
                                            Width="80"
                                            FontSize="11"
                                            Background="#2196F3"
                                            Foreground="White"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- 分页区域 -->
        <materialDesign:Card Grid.Row="2" Margin="10,0,10,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 分页信息 -->
                <TextBlock Grid.Column="0"
                           Text="{Binding PageInfo}"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="#666"/>

                <!-- 分页控件 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- 首页按钮 -->
                    <Button Command="{Binding FirstPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="首页"
                            Height="32"
                            Width="60"
                            Margin="0,0,5,0"
                            FontSize="11"/>

                    <!-- 上一页按钮 -->
                    <Button Command="{Binding PreviousPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="上一页"
                            Height="32"
                            Width="70"
                            Margin="0,0,5,0"
                            FontSize="11"/>

                    <TextBox Text="{Binding JumpToPage, UpdateSourceTrigger=PropertyChanged}"
                             Width="40"
                             Height="32"
                             FontSize="11"
                             TextAlignment="Center"
                             VerticalContentAlignment="Center"
                             Margin="0,0,5,0">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding GoToPageCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>
                    <TextBlock Text="页" 
                               VerticalAlignment="Center" 
                               FontSize="11" 
                               Margin="0,0,5,0"/>

                    <!-- 下一页按钮 -->
                    <Button Command="{Binding NextPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="下一页"
                            Height="32"
                            Width="60"
                            Margin="5,0,5,0"
                            FontSize="11"/>

                    <!-- 末页按钮 -->
                    <Button Command="{Binding LastPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="末页"
                            Height="32"
                            Width="50"
                            Margin="0,0,0,0"
                            FontSize="11"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>