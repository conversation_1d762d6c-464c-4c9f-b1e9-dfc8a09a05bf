using System;
using System.ComponentModel;

namespace WPF_MVVM_Test.MVVM_Model.LMZWorkSchedule
{
    /// <summary>
    /// 质检方案模型
    /// </summary>
    public class QualityInspectionPlan : INotifyPropertyChanged
    {
        private bool _isSelected;

        /// <summary>
        /// 质检方案ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 方案名称
        /// </summary>
        public string PlanName { get; set; } = string.Empty;

        /// <summary>
        /// 方案编号
        /// </summary>
        public string PlanCode { get; set; } = string.Empty;

        /// <summary>
        /// 检测类别
        /// </summary>
        public string DetectionCategory { get; set; } = string.Empty;

        /// <summary>
        /// 检测类型
        /// </summary>
        public string DetectionTypes { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = string.Empty;

        /// <summary>
        /// 版本
        /// </summary>
        public int Version { get; set; }

        /// <summary>
        /// 版本描述
        /// </summary>
        public string VersionDescription { get; set; } = string.Empty;

        /// <summary>
        /// 是否当前版本
        /// </summary>
        public bool IsCurrentVersion { get; set; }

        /// <summary>
        /// 生效时间
        /// </summary>
        public DateTime EffectiveTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}