using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Windows.Data;
using WPF_MVVM_Test.MVVM_Model.LMZWorkSchedule;

namespace WPF_MVVM_Test.MVVM_View.Converters
{
    /// <summary>
    /// 选择数量转换器
    /// </summary>
    public class SelectedCountConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ObservableCollection<QualityInspectionPlan> plans)
            {
                var selectedCount = plans.Count(p => p.IsSelected);
                var totalCount = plans.Count;
                return $"已选择 {selectedCount} / {totalCount} 个质检方案";
            }
            
            return "已选择 0 / 0 个质检方案";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}