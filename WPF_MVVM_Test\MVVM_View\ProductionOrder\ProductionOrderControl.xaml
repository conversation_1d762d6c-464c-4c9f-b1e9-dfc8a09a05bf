<UserControl x:Class="WPF_MVVM_Test.MVVM_View.ProductionOrder.ProductionOrderControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.ProductionOrder"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000"
             Background="#F5F5F5">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="{x:Type Button}">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="{x:Type Button}" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#52C41A"/>
        </Style>

        <Style x:Key="WarningButtonStyle" TargetType="{x:Type Button}" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#FA8C16"/>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="{x:Type Button}" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#FF4D4F"/>
        </Style>

        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="{x:Type TextBox}">
            <Setter Property="Height" Value="32"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 状态颜色转换器 -->
        <local:StatusColorConverter x:Key="StatusColorConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- 工单进度样式 -->
        <Style x:Key="ProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Height" Value="20"/>
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="Foreground" Value="#1890FF"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和操作按钮 -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="生产工单" FontSize="24" FontWeight="Bold" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding Message}" FontSize="14" Foreground="#666" VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="开始" Style="{StaticResource SuccessButtonStyle}" Margin="0,0,10,0"
                        Command="{Binding StartSelectedCommand}"/>
                <Button Content="取消" Style="{StaticResource WarningButtonStyle}" Margin="0,0,10,0"
                        Command="{Binding CancelSelectedCommand}"/>
                <Button Content="结束" Style="{StaticResource SuccessButtonStyle}" Margin="0,0,10,0"
                        Command="{Binding EndSelectedCommand}"/>
                <Button Content="撤回" Style="{StaticResource DangerButtonStyle}" Margin="0,0,10,0"
                        Command="{Binding RecallSelectedCommand}"/>
                <Button Content="🔄 刷新" Style="{StaticResource PrimaryButtonStyle}" Margin="0,0,10,0"
                        Command="{Binding RefreshCommand}"/>
            </StackPanel>
        </Grid>

        <!-- 搜索区域 -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 工单编号搜索 -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="工单编号" FontSize="14" Margin="0,0,0,8"/>
                    <TextBox Text="{Binding SearchOrderNumber, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource InputTextBoxStyle}" 
                             ToolTip="输入工单编号进行搜索"/>
                </StackPanel>

                <!-- 工单名称搜索 -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="工单名称" FontSize="14" Margin="0,0,0,8"/>
                    <TextBox Text="{Binding SearchProductName, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource InputTextBoxStyle}"
                             ToolTip="输入工单名称进行搜索"/>
                </StackPanel>

                <!-- 状态搜索 -->
                <StackPanel Grid.Column="4">
                    <TextBlock Text="状态" FontSize="14" Margin="0,0,0,8"/>
                    <ComboBox Height="32" ItemsSource="{Binding StatusOptions}"
                              SelectedValue="{Binding SearchStatus}"
                              DisplayMemberPath="Text" SelectedValuePath="Value"
                              BorderBrush="#D9D9D9" BorderThickness="1"/>
                </StackPanel>

                <!-- 搜索按钮 -->
                <StackPanel Grid.Column="5" Orientation="Horizontal" VerticalAlignment="Bottom">
                    <Button Content="🔍 搜索" Style="{StaticResource PrimaryButtonStyle}" Margin="0,0,10,0"
                            Command="{Binding SearchCommand}"/>
                    <Button Content="🗑️ 清空" Style="{StaticResource WarningButtonStyle}"
                            Command="{Binding ClearSearchCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 数据表格 -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 数据表格 -->
                <DataGrid Grid.Row="0" ItemsSource="{Binding ProductionOrders}" 
                          AutoGenerateColumns="False" CanUserAddRows="False"
                          CanUserDeleteRows="False" CanUserReorderColumns="True"
                          CanUserResizeColumns="True" CanUserResizeRows="False"
                          GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                          AlternatingRowBackground="#F8F9FA" RowBackground="White"
                          BorderThickness="0" HorizontalGridLinesBrush="#E8E8E8"
                          VerticalGridLinesBrush="Transparent" FontSize="14">
                    
                    <DataGrid.Columns>
                        <!-- 复选框列 -->
                        <DataGridTemplateColumn Header="" Width="50" IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="80" IsReadOnly="True"/>
                        <DataGridTextColumn Header="工单编号" Binding="{Binding OrderNumber}" Width="150" IsReadOnly="True"/>
                        <DataGridTextColumn Header="工单名称" Binding="{Binding ProductName}" Width="150" IsReadOnly="True"/>
                        
                        <!-- 工单进度列 -->
                        <DataGridTemplateColumn Header="工单进度" Width="300" IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- 工序1 -->
                                        <Border Grid.Column="0" Background="#20B2AA" Margin="1" CornerRadius="2">
                                            <TextBlock Text="{Binding Process1Progress}" 
                                                       HorizontalAlignment="Center" 
                                                       VerticalAlignment="Center" 
                                                       FontSize="10" 
                                                       Foreground="White"/>
                                        </Border>
                                        
                                        <!-- 工序2 -->
                                        <Border Grid.Column="1" Background="#9370DB" Margin="1" CornerRadius="2">
                                            <TextBlock Text="{Binding Process2Progress}" 
                                                       HorizontalAlignment="Center" 
                                                       VerticalAlignment="Center" 
                                                       FontSize="10" 
                                                       Foreground="White"/>
                                        </Border>
                                        
                                        <!-- 工序3 -->
                                        <Border Grid.Column="2" Background="#87CEEB" Margin="1" CornerRadius="2">
                                            <TextBlock Text="{Binding Process3Progress}" 
                                                       HorizontalAlignment="Center" 
                                                       VerticalAlignment="Center" 
                                                       FontSize="10" 
                                                       Foreground="White"/>
                                        </Border>
                                        
                                        <!-- 工序4 -->
                                        <Border Grid.Column="3" Background="#FFA500" Margin="1" CornerRadius="2">
                                            <TextBlock Text="{Binding Process4Progress}" 
                                                       HorizontalAlignment="Center" 
                                                       VerticalAlignment="Center" 
                                                       FontSize="10" 
                                                       Foreground="White"/>
                                        </Border>
                                    </Grid>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTextColumn Header="计划数量" Binding="{Binding PlanQuantity}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="实际生产数量" Binding="{Binding ActualQuantity}" Width="120" IsReadOnly="True"/>
                        <DataGridTextColumn Header="计划开工时间" Binding="{Binding StartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="150" IsReadOnly="True"/>
                        <DataGridTextColumn Header="计划完工时间" Binding="{Binding EndTime, StringFormat=yyyy-MM-dd HH:mm}" Width="150" IsReadOnly="True"/>
                        <DataGridTextColumn Header="实际开工时间" Binding="{Binding ActualStartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="150" IsReadOnly="True"/>
                        
                        <!-- 状态列 -->
                        <DataGridTemplateColumn Header="状态" Width="100" IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding StatusText}" 
                                               FontWeight="Bold"
                                               Foreground="{Binding StatusColor}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- 操作列 -->
                        <DataGridTemplateColumn Header="操作" Width="200" IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- 查看按钮 -->
                                        <Button Content="👁️ 查看" 
                                                Background="#1890FF" 
                                                Foreground="White" 
                                                BorderThickness="0" 
                                                Padding="8,4" 
                                                Margin="2" 
                                                Cursor="Hand" 
                                                FontSize="12"
                                                Command="{Binding DataContext.ViewProductionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                                                CommandParameter="{Binding}"/>
                                        
                                        <!-- 排产按钮 -->
                                        <Button Content="📅 排产" 
                                                Background="#52C41A" 
                                                Foreground="White" 
                                                BorderThickness="0" 
                                                Padding="8,4" 
                                                Margin="2" 
                                                Cursor="Hand" 
                                                FontSize="12"
                                                Visibility="{Binding CanSchedule, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                Command="{Binding DataContext.ScheduleProductionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                                                CommandParameter="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 分页信息 -->
                <Border Grid.Row="1" Background="#F8F9FA" BorderBrush="#E8E8E8" BorderThickness="0,1,0,0" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="{Binding PageInfo}" VerticalAlignment="Center" FontSize="14"/>

                        <!-- 分页控件 -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,20,0">
                            <Button Content="◀" Style="{StaticResource PrimaryButtonStyle}" Margin="0,0,5,0" Padding="10,6"
                                    Command="{Binding PreviousPageCommand}"/>
                            <TextBlock Text="{Binding CurrentPage}" VerticalAlignment="Center" Margin="10,0" FontSize="14" FontWeight="Bold"/>
                            <TextBlock Text="/" VerticalAlignment="Center" Margin="0,0,10,0" FontSize="14"/>
                            <TextBlock Text="{Binding TotalPages}" VerticalAlignment="Center" Margin="0,0,10,0" FontSize="14"/>
                            <Button Content="▶" Style="{StaticResource PrimaryButtonStyle}" Padding="10,6"
                                    Command="{Binding NextPageCommand}"/>
                        </StackPanel>

                        <!-- 每页条数选择 -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <ComboBox Width="80" Height="32" 
                                      ItemsSource="{Binding PageSizeOptions}"
                                      SelectedValue="{Binding PageSize}"
                                      BorderBrush="#D9D9D9" BorderThickness="1"/>
                            <TextBlock Text="条/页" VerticalAlignment="Center" Margin="5,0,10,0" FontSize="14"/>
                            <TextBox Width="50" Height="32" Text="{Binding GoToPage}" 
                                     BorderBrush="#D9D9D9" BorderThickness="1" 
                                     VerticalContentAlignment="Center"/>
                            <Button Content="页" Style="{StaticResource PrimaryButtonStyle}" 
                                    Margin="5,0,0,0" Padding="10,6"
                                    Command="{Binding GoToPageCommand}"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- 加载指示器 -->
        <Grid Grid.Row="3" Background="#80000000" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="🔄 正在加载数据..." FontSize="18" Foreground="White" HorizontalAlignment="Center"/>
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl> 
