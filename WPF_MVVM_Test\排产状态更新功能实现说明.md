# 排产状态更新功能实现说明

## 功能概述

在排产页面点击"确认排产"按钮时，除了原有的批量添加工单任务功能外，新增了调用后端API更新生产工单状态的功能。

## 实现内容

### 1. 新增请求模型

**文件**: `WPF_MVVM_Test/MVVM_Model/ProductionOrder/UpdateProductionOrderStatusRequest.cs`

**新增的请求模型**:
```csharp
public class UpdateProductionOrderStatusRequest
{
    /// <summary>
    /// 生产工单ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedBy { get; set; } = string.Empty;
}
```

### 2. 新增服务方法

**文件**: `WPF_MVVM_Test/Services/ProductionOrder/ProductionOrderService.cs`

**新增的服务方法**:
```csharp
/// <summary>
/// 更新生产工单状态
/// </summary>
/// <param name="request">更新状态请求</param>
/// <returns></returns>
public async Task<ApiResponse> UpdateProductionOrderStatusAsync(UpdateProductionOrderStatusRequest request)
{
    try
    {
        var url = "http://localhost:64922/api/ProductionOrder/UpdateProductionOrderStatus";
        
        System.Diagnostics.Debug.WriteLine($"更新生产工单状态URL: {url}");
        System.Diagnostics.Debug.WriteLine($"更新状态请求: Id={request.Id}, Status={request.Status}, UpdatedBy={request.UpdatedBy}");
        
        var jsonContent = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PostAsync(url, content);
        var responseContent = await response.Content.ReadAsStringAsync();
        
        System.Diagnostics.Debug.WriteLine($"更新状态API响应: {responseContent}");
        
        if (response.IsSuccessStatusCode)
        {
            var result = JsonSerializer.Deserialize<ApiResponse>(responseContent, _jsonOptions);
            return result ?? new ApiResponse { IsSuc = false, Msg = "数据解析失败" };
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"更新状态API请求失败: {response.StatusCode} - {response.ReasonPhrase}");
            return new ApiResponse
            {
                IsSuc = false,
                Code = (int)response.StatusCode,
                Msg = $"请求失败: {response.ReasonPhrase}"
            };
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"更新生产工单状态异常: {ex.Message}");
        return new ApiResponse
        {
            IsSuc = false,
            Code = 500,
            Msg = $"请求异常: {ex.Message}"
        };
    }
}
```

### 3. 修改排产确认逻辑

**文件**: `WPF_MVVM_Test/MVVM_ViewModel/ProductionOrder/ScheduleProductionDialogViewModel.cs`

**修改的ConfirmSchedule方法**:
```csharp
if (response.IsSuc)
{
    System.Windows.MessageBox.Show($"成功保存 {allTasks.Count} 个任务", "成功", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    
    // 调用更新生产工单状态API
    await UpdateProductionOrderStatus();
    
    DialogResult = true;
}
```

**新增的UpdateProductionOrderStatus方法**:
```csharp
/// <summary>
/// 更新生产工单状态
/// </summary>
private async Task UpdateProductionOrderStatus()
{
    try
    {
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] === 开始更新生产工单状态 ===");
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 生产工单ID: {_originalOrder.Id}");
        
        var updateStatusRequest = new UpdateProductionOrderStatusRequest
        {
            Id = _originalOrder.Id,
            Status = "进行中", // 排产完成后状态改为"进行中"
            UpdatedBy = "系统用户" // 可以根据实际需要设置更新人
        };
        
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 更新状态请求:");
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}]   ID: {updateStatusRequest.Id}");
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}]   Status: {updateStatusRequest.Status}");
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}]   UpdatedBy: {updateStatusRequest.UpdatedBy}");
        
        var response = await _productionOrderService.UpdateProductionOrderStatusAsync(updateStatusRequest);
        
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 更新状态API响应:");
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}]   IsSuc: {response.IsSuc}");
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}]   Code: {response.Code}");
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}]   Msg: {response.Msg}");
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] === 更新状态结束 ===");
        
        if (response.IsSuc)
        {
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 生产工单状态更新成功");
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 生产工单状态更新失败: {response.Msg}");
            // 注意：这里不显示错误消息给用户，因为任务已经成功保存
            // 状态更新失败不影响排产的成功
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 更新生产工单状态异常: {ex.Message}");
        // 注意：这里不显示错误消息给用户，因为任务已经成功保存
        // 状态更新异常不影响排产的成功
    }
}
```

## API接口信息

### 1. 请求接口
- **URL**: `http://localhost:64922/api/ProductionOrder/UpdateProductionOrderStatus`
- **方法**: POST
- **Content-Type**: application/json

### 2. 请求参数
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "string",
  "updatedBy": "string"
}
```

### 3. 响应结构
```json
{
  "isSuc": false,
  "code": 300,
  "msg": "生产工单不存在或已被删除"
}
```

## 功能流程

### 1. 排产确认流程
1. 用户点击"确认排产"按钮
2. 系统收集所有工序的任务数据
3. 验证任务数据的完整性
4. 调用批量添加工单任务API
5. **新增**: 如果任务添加成功，调用更新生产工单状态API
6. 显示操作结果

### 2. 状态更新流程
1. 创建更新状态请求对象
2. 设置状态为"已排产"
3. 设置更新人为"系统用户"
4. 调用后端API更新状态
5. 记录API响应结果
6. 记录调试日志

## 状态值说明

### 1. 当前实现的状态值
- **"进行中"**: 表示生产工单已完成排产，正在生产过程中

### 2. 可扩展的状态值
- **"待排产"**: 生产工单创建后，等待排产
- **"排产中"**: 正在进行排产操作
- **"已排产"**: 排产完成，可以开始生产
- **"进行中"**: 正在生产
- **"已完成"**: 生产完成
- **"已暂停"**: 生产暂停
- **"已取消"**: 生产取消

## 错误处理

### 1. 状态更新失败处理
- **不影响排产成功**: 即使状态更新失败，排产操作仍然被认为是成功的
- **记录错误日志**: 所有错误都会记录到调试日志中
- **不显示错误消息**: 不会向用户显示状态更新失败的消息

### 2. 异常处理
- **网络异常**: 捕获并记录网络相关异常
- **API异常**: 捕获并记录API调用异常
- **数据解析异常**: 捕获并记录JSON解析异常

## 调试支持

### 1. 详细日志记录
- **请求开始**: 记录状态更新开始时间
- **请求参数**: 记录ID、状态、更新人信息
- **API响应**: 记录完整的API响应内容
- **操作结果**: 记录成功或失败状态
- **异常信息**: 记录详细的异常信息

### 2. 日志格式
```
[时间] === 开始更新生产工单状态 ===
[时间] 生产工单ID: xxx
[时间] 更新状态请求:
[时间]   ID: xxx
[时间]   Status: 进行中
[时间]   UpdatedBy: 系统用户
[时间] 更新状态API响应:
[时间]   IsSuc: true/false
[时间]   Code: xxx
[时间]   Msg: xxx
[时间] === 更新状态结束 ===
```

## 使用方法

### 1. 基本使用
1. 打开排产对话框
2. 配置工序和任务信息
3. 点击"确认排产"按钮
4. 系统自动执行排产和状态更新

### 2. 验证方法
1. 查看Visual Studio输出窗口的调试日志
2. 确认两个API调用都成功
3. 检查生产工单状态是否已更新

## 扩展功能

### 1. 状态值配置
可以将状态值配置化，支持更多状态：
```csharp
// 可以添加状态配置
public static class ProductionOrderStatus
{
    public const string Pending = "待排产";
    public const string Scheduling = "排产中";
    public const string Scheduled = "已排产";
    public const string Producing = "生产中";
    public const string Completed = "已完成";
    public const string Paused = "已暂停";
    public const string Cancelled = "已取消";
}
```

### 2. 更新人配置
可以根据实际需求设置更新人：
```csharp
// 可以从用户登录信息获取
UpdatedBy = GetCurrentUserName();
```

### 3. 状态更新确认
可以添加状态更新成功的用户提示：
```csharp
if (response.IsSuc)
{
    System.Windows.MessageBox.Show("生产工单状态已更新为进行中", "状态更新成功", 
        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
}
```

## 总结

成功实现了排产状态更新功能，包括：

1. **完整的API集成** - 调用后端更新状态接口
2. **错误处理机制** - 不影响主要排产功能
3. **详细日志记录** - 便于调试和监控
4. **扩展性设计** - 支持更多状态和配置

现在点击"确认排产"按钮会同时完成：
- 批量添加工单任务
- 更新生产工单状态为"进行中"

确保生产工单的状态与排产操作保持同步。 