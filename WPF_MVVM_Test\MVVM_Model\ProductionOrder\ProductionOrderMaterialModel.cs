using System.Collections.Generic;

namespace WPF_MVVM_Test.MVVM_Model.ProductionOrder
{
    /// <summary>
    /// 生产工单物料响应模型
    /// </summary>
    public class ProductionOrderMaterialResponse
    {
        public ProductionOrderMaterialData Data { get; set; }
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 生产工单物料数据
    /// </summary>
    public class ProductionOrderMaterialData
    {
        public string ProductionOrderId { get; set; } = string.Empty;
        public string OrderNumber { get; set; } = string.Empty;
        public string OrderName { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string ProductNumber { get; set; } = string.Empty;
        public double PlanQuantity { get; set; }
        public string Unit { get; set; } = string.Empty;
        public List<MaterialItem> Materials { get; set; } = new List<MaterialItem>();
        public double TotalMaterialQuantity { get; set; }
    }

    /// <summary>
    /// 物料项
    /// </summary>
    public class MaterialItem
    {
        public int Sequence { get; set; }
        public string MaterialNumber { get; set; } = string.Empty;
        public string MaterialName { get; set; } = string.Empty;
        public string Specification { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public double EstimatedUsage { get; set; }
        public string MaterialRatio { get; set; } = string.Empty;
        public string MaterialId { get; set; } = string.Empty;
        public int Level { get; set; }
        public string ParentItemId { get; set; } = string.Empty;
    }
}

