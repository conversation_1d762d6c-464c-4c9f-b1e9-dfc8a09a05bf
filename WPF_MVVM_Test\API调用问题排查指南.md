# API调用问题排查指南

## 问题描述

API测试返回400 Bad Request错误，需要进行系统性的问题排查。

## 排查步骤

### 1. 检查后端服务状态
- 确认后端服务是否运行在 `http://localhost:64922`
- 使用浏览器访问 `http://localhost:64922/swagger` 查看API文档
- 确认API端点是否存在

### 2. 验证API端点
- 检查URL是否正确：`api/ProductionOrder/BatchAddWorkOrderTasks/work-order-tasks/batch`
- 确认HTTP方法为POST
- 确认Content-Type为application/json

### 3. 数据格式验证
- 使用"🔧 直接测试"按钮进行测试
- 检查JSON数据格式是否与curl命令一致
- 验证所有必需字段是否都有值

### 4. 调试日志分析
- 查看Visual Studio输出窗口的调试日志
- 检查请求数据的JSON格式
- 分析响应状态码和错误信息

### 5. 常见问题及解决方案

#### 问题1: 缺少"dto"字段
**症状**: 400错误，错误信息显示"The dto field is required"
**解决方案**: 
- 使用`BatchAddWorkOrderTasksDtoRequest`模型包装请求数据
- 确保请求JSON包含dto字段
- 检查数据模型结构

#### 问题2: 任务列表为空
**症状**: 400错误，错误信息显示"工单任务列表不能为空"
**解决方案**:
- 使用"📊 任务统计"按钮查看当前任务数据状态
- 确保每个工序都有任务数据
- 如果没有任务数据，系统会自动创建默认任务
- 检查任务数据收集逻辑

#### 问题3: GUID格式错误
**症状**: 400错误，错误信息显示"could not be converted to System.Nullable<Guid>"
**解决方案**:
- 将processRouteId和processStepId字段类型改为Guid?
- 确保GUID字符串格式正确
- 添加GUID解析验证

#### 问题4: JSON序列化格式不正确
**症状**: 400错误，后端无法解析请求数据
**解决方案**: 
- 检查JsonPropertyName特性是否正确
- 确认JSON序列化选项设置
- 使用"�� 直接测试"按钮验证

#### 问题5: 必需字段缺失或格式错误
**症状**: 400错误，后端验证失败
**解决方案**:
- 确保所有必需字段都有有效值
- 检查数据类型是否正确
- 验证日期时间格式

#### 问题6: 生产工单ID无效
**症状**: 400错误，找不到对应的生产工单
**解决方案**:
- 确认生产工单ID是否存在
- 检查工单状态是否允许排产
- 验证工单权限

#### 问题7: 网络连接问题
**症状**: 连接超时或网络错误
**解决方案**:
- 检查网络连接
- 确认防火墙设置
- 验证代理配置

### 6. 测试方法

#### 方法1: 使用"📊 任务统计"按钮
- 查看当前任务数据状态
- 显示每个工序的任务数量
- 帮助诊断任务数据收集问题

#### 方法2: 使用"🧪 测试API"按钮
- 使用模型类序列化的数据
- 适合测试数据模型和序列化逻辑

#### 方法3: 使用"🔧 直接测试"按钮
- 使用硬编码的JSON数据
- 完全匹配curl命令格式
- 适合验证API端点本身

#### 方法4: 使用curl命令
```bash
curl -X 'POST' \
  'http://localhost:64922/api/ProductionOrder/BatchAddWorkOrderTasks/work-order-tasks/batch' \
  -H 'accept: text/plain' \
  -H 'Content-Type: application/json' \
  -d '{
  "dto": {
    "tasks": [
      {
        "sequenceNumber": 0,
        "taskNumber": "string",
        "taskName": "string",
        "productionOrderId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "stationName": "string",
        "processCode": "string",
        "processName": "string",
        "processFlow": "string",
        "processType": "string",
        "taskColor": "string",
        "planQuantity": 0.01,
        "planStartTime": "2025-08-01T13:27:50.587Z",
        "planEndTime": "2025-08-01T13:27:50.587Z",
        "planDuration": 0.01,
        "status": "string",
        "processRouteId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "processStepId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "priority": 4,
        "remarks": "string"
      }
    ]
  }
}'
```

### 7. 调试技巧

#### 查看详细日志
1. 打开Visual Studio
2. 运行应用程序
3. 打开"输出"窗口
4. 点击测试按钮
5. 查看调试日志输出

#### 分析错误信息
- 400错误通常表示请求格式问题
- 检查响应内容中的具体错误信息
- 根据错误信息调整数据格式

#### 逐步验证
1. 先测试API端点是否可访问
2. 再测试数据格式是否正确
3. 最后测试业务逻辑

### 8. 联系后端开发人员

如果以上步骤都无法解决问题，请提供以下信息给后端开发人员：

1. 完整的错误响应内容
2. 请求的JSON数据
3. 使用的API端点URL
4. 调试日志输出
5. 测试的curl命令结果

### 9. 临时解决方案

如果API暂时无法使用，可以考虑：

1. 使用模拟数据进行前端测试
2. 实现本地数据存储
3. 添加API调用开关，支持离线模式

## 总结

通过系统性的排查，大多数API调用问题都能得到解决。关键是要：

1. 确认后端服务状态
2. 验证API端点正确性
3. 检查数据格式一致性
4. 分析详细的错误信息
5. 使用多种测试方法验证 