# 批量排产API调用实现说明

## 功能概述

本功能实现了点击"确定排产"按钮后调用后端批量添加任务API的功能，完全匹配提供的curl命令格式。

## 重要修复

### 1. DTO包装问题
- **问题**: 后端API期望请求数据包含在"dto"字段中
- **解决方案**: 创建了`BatchAddWorkOrderTasksDtoRequest`模型，将原始请求包装在dto字段中

### 2. GUID格式问题
- **问题**: processRouteId和processStepId字段需要是有效的GUID格式
- **解决方案**: 将这两个字段的类型从string改为Guid?，并添加了GUID解析逻辑

## API接口信息

- **URL**: `http://localhost:64922/api/ProductionOrder/BatchAddWorkOrderTasks/work-order-tasks/batch`
- **方法**: POST
- **Content-Type**: application/json
- **Accept**: text/plain

## 实现文件

### 1. ProcessService.cs
- **位置**: `WPF_MVVM_Test/MVVM_Services/ProcessService.cs`
- **方法**: `BatchAddWorkOrderTasksAsync(object batchTaskData)`
- **功能**: 封装API调用逻辑，包含详细的调试日志

### 2. ScheduleProductionDialogViewModel.cs
- **位置**: `WPF_MVVM_Test/MVVM_ViewModel/ProductionOrder/ScheduleProductionDialogViewModel.cs`
- **方法**: `ConfirmSchedule()`
- **功能**: 处理确定排产按钮点击事件，收集任务数据并调用API

### 3. 数据模型
- **位置**: `WPF_MVVM_Test/MVVM_Model/Process/ProcessTaskModel.cs`
- **类**: `WorkOrderTaskRequest`, `BatchAddWorkOrderTasksRequest`, `BatchAddWorkOrderTasksDtoRequest`
- **功能**: 定义API请求的数据结构

## 数据结构

### BatchAddWorkOrderTasksDtoRequest (主要请求模型)
```csharp
public class BatchAddWorkOrderTasksDtoRequest
{
    [JsonPropertyName("dto")]
    public BatchAddWorkOrderTasksRequest Dto { get; set; } = new BatchAddWorkOrderTasksRequest();
}
```

### BatchAddWorkOrderTasksRequest
```csharp
public class BatchAddWorkOrderTasksRequest
{
    [JsonPropertyName("tasks")]
    public List<WorkOrderTaskRequest> Tasks { get; set; } = new List<WorkOrderTaskRequest>();
}
```

### WorkOrderTaskRequest
```csharp
public class WorkOrderTaskRequest
{
    public int SequenceNumber { get; set; }           // 序号
    public string TaskNumber { get; set; }            // 任务编号
    public string TaskName { get; set; }              // 任务名称
    public string ProductionOrderId { get; set; }     // 生产工单ID
    public string StationName { get; set; }           // 工位名称
    public string ProcessCode { get; set; }           // 工序代码
    public string ProcessName { get; set; }           // 工序名称
    public string ProcessFlow { get; set; }           // 工艺流程
    public string ProcessType { get; set; }           // 工序类型
    public string TaskColor { get; set; }             // 任务颜色
    public decimal PlanQuantity { get; set; }         // 计划数量
    public DateTime PlanStartTime { get; set; }       // 计划开始时间
    public DateTime PlanEndTime { get; set; }         // 计划结束时间
    public decimal PlanDuration { get; set; }         // 计划时长
    public string Status { get; set; }                // 状态
    public Guid? ProcessRouteId { get; set; }         // 工艺路线ID (GUID格式)
    public Guid? ProcessStepId { get; set; }          // 工序步骤ID (GUID格式)
    public int Priority { get; set; }                 // 优先级
    public string Remarks { get; set; }               // 备注
}
```

## 请求JSON格式

正确的请求格式应该是：
```json
{
  "dto": {
    "tasks": [
      {
        "sequenceNumber": 0,
        "taskNumber": "string",
        "taskName": "string",
        "productionOrderId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "stationName": "string",
        "processCode": "string",
        "processName": "string",
        "processFlow": "string",
        "processType": "string",
        "taskColor": "string",
        "planQuantity": 0.01,
        "planStartTime": "2025-08-01T13:27:50.587Z",
        "planEndTime": "2025-08-01T13:27:50.587Z",
        "planDuration": 0.01,
        "status": "string",
        "processRouteId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "processStepId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "priority": 4,
        "remarks": "string"
      }
    ]
  }
}
```

## 使用流程

### 1. 打开排产对话框
- 在生产工单列表中找到状态为"待排产"的工单
- 点击操作列中的"排产"链接
- 系统弹出排产对话框

### 2. 配置任务信息
- 在对话框中配置各个工序的任务信息
- 包括任务名称、工位、计划时间、数量等
- 可以添加、编辑、删除任务

### 3. 确定排产
- 点击"✅ 确定排产"按钮
- 系统会显示确认对话框
- 确认后调用批量添加API

### 4. API调用过程
1. 收集所有工序的任务数据
2. 验证数据完整性
3. 构建API请求对象（包含DTO包装）
4. 调用后端API
5. 处理响应结果

## 调试功能

### 测试API按钮
- 在排产对话框中添加了"🧪 测试API"按钮
- 点击后会发送一个测试任务到后端
- 用于验证API连接和数据结构是否正确

### 直接测试按钮
- 添加了"🔧 直接测试"按钮
- 使用硬编码的JSON数据，完全匹配后端期望的格式
- 适合验证API端点本身

### 调试日志
- 在ProcessService中添加了详细的调试日志
- 记录API调用的URL、请求数据、响应结果
- 可通过Visual Studio的输出窗口查看

## 错误处理

### 数据验证
- 验证生产工单ID不能为空
- 验证任务数量必须大于0
- 验证时间字段的有效性
- 验证GUID字段格式

### 异常处理
- 捕获HTTP请求异常
- 捕获JSON序列化异常
- 捕获GUID解析异常
- 显示友好的错误提示

## 注意事项

1. **API地址**: 确保后端服务运行在 `http://localhost:64922`
2. **数据格式**: 确保所有必需字段都有有效值
3. **时间格式**: 时间字段使用ISO 8601格式
4. **GUID格式**: processRouteId和processStepId必须是有效的GUID格式
5. **DTO包装**: 请求数据必须包含在dto字段中
6. **网络连接**: 确保网络连接正常
7. **权限验证**: 确保有调用API的权限

## 测试建议

1. 首先使用"🔧 直接测试"按钮验证API连接
2. 检查调试日志确认请求数据格式正确
3. 验证后端API返回的响应格式
4. 测试各种异常情况的处理

## 扩展功能

- 可以添加重试机制
- 可以添加请求超时设置
- 可以添加更详细的错误分类
- 可以添加API调用的进度提示 