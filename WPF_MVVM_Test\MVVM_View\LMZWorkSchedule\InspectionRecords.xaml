<UserControl x:Class="WPF_MVVM_Test.MVVM_View.LMZWorkSchedule.InspectionRecords"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <Border Grid.Row="0" Background="#F5F5F5" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="工单名称:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBox x:Name="OrderNameTextBox" Width="200" Height="25" 
                         Text="{Binding OrderNameFilter, UpdateSourceTrigger=PropertyChanged}" 
                         Margin="0,0,10,0"/>
                <Button Content="搜索" Command="{Binding SearchCommand}" 
                        Width="80" Height="30" Margin="0,0,10,0"/>
                <Button Content="重置" Command="{Binding ResetCommand}" 
                        Width="80" Height="30"/>
            </StackPanel>
        </Border>

        <!-- 数据表格 -->
        <DataGrid Grid.Row="1" x:Name="InspectionRecordsDataGrid" 
                  ItemsSource="{Binding InspectionRecords}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  Margin="5">
            <DataGrid.Columns>
                <DataGridTextColumn Header="序号" Binding="{Binding SequenceNumber}" Width="60"/>
                <DataGridTextColumn Header="任务编号" Binding="{Binding TaskNumber}" Width="120"/>
                <DataGridTextColumn Header="质检名称" Binding="{Binding InspectionName}" Width="100"/>
                <DataGridTextColumn Header="质检类型" Binding="{Binding InspectionType}" Width="80"/>
                <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                <DataGridTextColumn Header="工序名称" Binding="{Binding ProcessName}" Width="100"/>
                <DataGridTextColumn Header="质检员" Binding="{Binding InspectorName}" Width="80"/>
                <DataGridTextColumn Header="报工数量" Binding="{Binding ReportedQuantity}" Width="80"/>
                <DataGridTextColumn Header="检测数量" Binding="{Binding TestedQuantity}" Width="80"/>
                <DataGridTextColumn Header="合格数量" Binding="{Binding QualifiedQuantity}" Width="80"/>
                <DataGridTextColumn Header="不合格数量" Binding="{Binding UnqualifiedQuantity}" Width="90"/>
                <DataGridTextColumn Header="报工时间" Binding="{Binding ReportTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130"/>
                <DataGridTextColumn Header="质检时间" Binding="{Binding InspectionTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130"/>
                <DataGridTextColumn Header="工单号" Binding="{Binding OrderNumber}" Width="150"/>
                <DataGridTextColumn Header="工单名称" Binding="{Binding OrderName}" Width="100"/>
                <DataGridTextColumn Header="规格" Binding="{Binding Specification}" Width="80"/>
                <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="60"/>
                <DataGridTextColumn Header="计划数量" Binding="{Binding OrderPlanQuantity}" Width="80"/>
                <DataGridTextColumn Header="实际数量" Binding="{Binding OrderActualQuantity}" Width="80"/>
                <DataGridTextColumn Header="工单状态" Binding="{Binding OrderStatus}" Width="80"/>
                <DataGridTextColumn Header="备注" Binding="{Binding Remark}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 分页控件 -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="首页" Command="{Binding FirstPageCommand}" 
                        Width="60" Height="30" Margin="5,0"/>
                <Button Content="上一页" Command="{Binding PreviousPageCommand}" 
                        Width="60" Height="30" Margin="5,0"/>
                <TextBlock Text="{Binding CurrentPage}" VerticalAlignment="Center" 
                           Margin="10,0" FontWeight="Bold"/>
                <TextBlock Text="/" VerticalAlignment="Center" Margin="5,0"/>
                <TextBlock Text="{Binding TotalPages}" VerticalAlignment="Center" 
                           Margin="0,0,10,0" FontWeight="Bold"/>
                <Button Content="下一页" Command="{Binding NextPageCommand}" 
                        Width="60" Height="30" Margin="5,0"/>
                <Button Content="末页" Command="{Binding LastPageCommand}" 
                        Width="60" Height="30" Margin="5,0"/>
                <TextBlock Text="每页" VerticalAlignment="Center" Margin="20,0,5,0"/>
                <ComboBox x:Name="PageSizeComboBox" Width="60" Height="25" 
                          SelectionChanged="PageSizeComboBox_SelectionChanged">
                    <ComboBoxItem Content="10" IsSelected="True"/>
                    <ComboBoxItem Content="20"/>
                    <ComboBoxItem Content="50"/>
                </ComboBox>
                <TextBlock Text="条" VerticalAlignment="Center" Margin="5,0,20,0"/>
                <TextBlock Text="{Binding TotalRecords, StringFormat=共 {0} 条记录}" 
                           VerticalAlignment="Center" FontWeight="Bold"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>