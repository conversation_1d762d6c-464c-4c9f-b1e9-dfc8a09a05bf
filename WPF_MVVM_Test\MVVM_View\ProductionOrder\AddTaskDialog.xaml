<Window x:Class="WPF_MVVM_Test.MVVM_View.ProductionOrder.AddTaskDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:WPF_MVVM_Test.MVVM_View.Converters"
        mc:Ignorable="d"
        Title="新增任务" Height="600" Width="800" 
        WindowStartupLocation="CenterOwner" 
        ResizeMode="NoResize">
    
    <Window.Resources>
        <!-- 转换器 -->
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        
        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="{x:Type TextBox}">
            <Setter Property="Height" Value="32"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- ComboBox样式 -->
        <Style x:Key="InputComboBoxStyle" TargetType="{x:Type ComboBox}">
            <Setter Property="Height" Value="32"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>

        <!-- 必填标签样式 -->
        <Style x:Key="RequiredLabelStyle" TargetType="{x:Type TextBlock}" BasedOn="{StaticResource LabelStyle}">
            <Setter Property="Foreground" Value="Red"/>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="{x:Type Button}">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}" CornerRadius="4" Padding="16,8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#40A9FF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#096DD9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="{x:Type Button}">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#595959"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4" Padding="16,8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#40A9FF"/>
                                <Setter Property="Foreground" Value="#40A9FF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 颜色选择器样式 -->
        <Style x:Key="ColorPickerStyle" TargetType="{x:Type Button}">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <Grid>
                                <Rectangle Fill="{Binding SelectedColor}" Margin="4"/>
                                <Path Data="M7,10 L12,15 L17,10" Stroke="White" StrokeThickness="2" 
                                      HorizontalAlignment="Right" VerticalAlignment="Bottom" Margin="0,0,4,4"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5" Margin="10">
        <Border Background="White" CornerRadius="8" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 标题栏 -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <TextBlock Text="新增任务" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                    <Button Content="✕" FontSize="16" Width="30" Height="30" 
                            HorizontalAlignment="Right" Background="Transparent" BorderThickness="0"
                            Command="{Binding CloseCommand}"/>
                </Grid>

                <!-- 表单内容 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="400">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 第一行 -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="任务编号" Style="{StaticResource LabelStyle}"/>
                            </StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Text="{Binding TaskNumber, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource InputTextBoxStyle}" Grid.Column="0" Margin="0,0,10,0" 
                                         IsEnabled="{Binding IsSystemNumberEnabled, Converter={StaticResource InverseBooleanConverter}}"/>
                                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                    <TextBlock Text="系统编号" Style="{StaticResource LabelStyle}" VerticalAlignment="Center"/>
                                    <ToggleButton IsChecked="{Binding IsSystemNumber}" 
                                                  Width="50" Height="24" 
                                                  VerticalAlignment="Center"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="任务名称" Style="{StaticResource LabelStyle}"/>
                            </StackPanel>
                            <TextBox Text="{Binding TaskName, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                        </StackPanel>

                        <!-- 第二行 -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="站点名称" Style="{StaticResource LabelStyle}"/>
                            </StackPanel>
                            <ComboBox ItemsSource="{Binding StationNames}" 
                                      SelectedItem="{Binding SelectedStationName}"
                                      Style="{StaticResource InputComboBoxStyle}"
                                      DisplayMemberPath="Name"/>
                        </StackPanel>

                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,15">
                            <TextBlock Text="站点编号" Style="{StaticResource LabelStyle}" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding StationId, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                        </StackPanel>

                        <!-- 第三行 -->
                        <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="任务颜色" Style="{StaticResource LabelStyle}"/>
                            </StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Text="{Binding TaskColor, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource InputTextBoxStyle}" Grid.Column="0" Margin="0,0,10,0"/>
                                <Button Background="{Binding TaskColor}" Grid.Column="1"
                                        Style="{StaticResource ColorPickerStyle}"
                                        Command="{Binding PickColorCommand}"/>
                            </Grid>
                        </StackPanel>

                        <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="计划数量" Style="{StaticResource LabelStyle}"/>
                            </StackPanel>
                            <TextBox Text="{Binding PlanQuantity, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource InputTextBoxStyle}"/>
                        </StackPanel>

                        <!-- 第四行 -->
                        <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,10,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="开工时间" Style="{StaticResource LabelStyle}"/>
                            </StackPanel>
                            <DatePicker SelectedDate="{Binding PlanStartTime, Mode=TwoWay}"
                                        Height="32" Margin="0"/>
                        </StackPanel>

                        <StackPanel Grid.Row="3" Grid.Column="1" Margin="10,0,0,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="完工时间" Style="{StaticResource LabelStyle}"/>
                            </StackPanel>
                            <DatePicker SelectedDate="{Binding PlanEndTime, Mode=TwoWay}"
                                        Height="32" Margin="0"/>
                        </StackPanel>

                        <!-- 第五行 - 系统编号开关 -->
                        <StackPanel Grid.Row="4" Grid.Column="0" Margin="0,0,10,15">
                            <!-- 预留空间 -->
                        </StackPanel>

                        <StackPanel Grid.Row="4" Grid.Column="1" Margin="10,0,0,15">
                            <!-- 预留空间 -->
                        </StackPanel>

                        <!-- 备注 -->
                        <StackPanel Grid.Row="5" Grid.ColumnSpan="2" Margin="0,0,0,15">
                            <TextBlock Text="备注" Style="{StaticResource LabelStyle}" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Remarks, UpdateSourceTrigger=PropertyChanged}"
                                     Height="80" Style="{StaticResource InputTextBoxStyle}"
                                     AcceptsReturn="True" TextWrapping="Wrap"
                                     VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>

                <!-- 按钮区域 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                    <Button Content="确定" Width="80" Height="32" Margin="0,0,10,0"
                            Style="{StaticResource PrimaryButtonStyle}" Command="{Binding ConfirmCommand}"/>
                    <Button Content="取消" Width="80" Height="32"
                            Style="{StaticResource SecondaryButtonStyle}" Command="{Binding CancelCommand}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window> 