using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.ProductionOrder
{
    /// <summary>
    /// 工艺路线响应模型
    /// </summary>
    public class ProductionOrderRoutingResponse
    {
        [JsonPropertyName("data")]
        public ProductionOrderRoutingData Data { get; set; } = new ProductionOrderRoutingData();
        
        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }
        
        [JsonPropertyName("code")]
        public int Code { get; set; }
        
        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 工艺路线数据
    /// </summary>
    public class ProductionOrderRoutingData
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;
        
        [JsonPropertyName("processRouteNumber")]
        public string ProcessRouteNumber { get; set; } = string.Empty;
        
        [JsonPropertyName("processRouteName")]
        public string ProcessRouteName { get; set; } = string.Empty;
    }
} 