using System.Windows.Controls;
using WPF_MVVM_Test.MVVM_ViewModel.WorkOrderTask;

namespace WPF_MVVM_Test.MVVM_View.WorkOrderTask
{
    /// <summary>
    /// WorkOrderTaskControl.xaml 的交互逻辑
    /// </summary>
    public partial class WorkOrderTaskControl : System.Windows.Controls.UserControl
    {
        public WorkOrderTaskControl()
        {
            InitializeComponent();

            // 设置DataContext为WorkOrderTaskViewModel
            var viewModel = new WorkOrderTaskViewModel();
            this.DataContext = viewModel;

            // 调试：检查ViewModel是否正确创建
            System.Diagnostics.Debug.WriteLine($"WorkOrderTaskControl创建完成，ViewModel: {viewModel != null}");
            System.Diagnostics.Debug.WriteLine($"FilteredTasks数量: {viewModel.FilteredTasks?.Count ?? -1}");
        }
    }
}
