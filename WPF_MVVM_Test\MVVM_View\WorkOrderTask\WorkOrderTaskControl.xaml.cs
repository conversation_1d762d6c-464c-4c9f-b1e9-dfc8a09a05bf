using System.Windows.Controls;
using WPF_MVVM_Test.MVVM_ViewModel.WorkOrderTask;

namespace WPF_MVVM_Test.MVVM_View.WorkOrderTask
{
    /// <summary>
    /// WorkOrderTaskControl.xaml 的交互逻辑
    /// </summary>
    public partial class WorkOrderTaskControl : System.Windows.Controls.UserControl
    {
        public WorkOrderTaskControl()
        {
            InitializeComponent();

            // 设置DataContext为WorkOrderTaskViewModel
            this.DataContext = new WorkOrderTaskViewModel();
        }
    }
}
