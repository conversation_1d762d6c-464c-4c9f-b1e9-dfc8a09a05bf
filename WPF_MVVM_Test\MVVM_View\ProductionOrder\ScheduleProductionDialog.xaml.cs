using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using System.ComponentModel;

namespace WPF_MVVM_Test.MVVM_View.ProductionOrder
{
    public partial class ScheduleProductionDialog : Window
    {
        public ScheduleProductionDialog(object viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 排产对话框初始化完成");
            
            // 监听ViewModel的DialogResult属性变化
            if (viewModel is INotifyPropertyChanged notifyPropertyChanged)
            {
                notifyPropertyChanged.PropertyChanged += OnViewModelPropertyChanged;
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 已添加属性变化监听");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewModel未实现INotifyPropertyChanged");
            }
        }
        
        private void OnViewModelPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 属性变化事件: {e.PropertyName}");
            
            if (e.PropertyName == "DialogResult")
            {
                try
                {
                    // 使用反射获取DialogResult属性值
                    var dialogResultProperty = sender.GetType().GetProperty("DialogResult");
                    if (dialogResultProperty != null)
                    {
                        bool dialogResult = (bool)dialogResultProperty.GetValue(sender);
                        System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] DialogResult变化为: {dialogResult}");
                        
                        if (dialogResult)
                        {
                            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 准备关闭对话框（确认）");
                            // 当DialogResult为true时，设置对话框结果为true并关闭
                            this.DialogResult = true;
                            this.Close();
                            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 对话框关闭完成（确认）");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 准备关闭对话框（取消）");
                            // 当DialogResult为false时，设置对话框结果为false并关闭
                            this.DialogResult = false;
                            this.Close();
                            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 对话框关闭完成（取消）");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"设置对话框结果时发生异常: {ex.Message}");
                }
            }
        }
        
        protected override void OnClosed(EventArgs e)
        {
            // 清理事件监听
            if (DataContext is INotifyPropertyChanged notifyPropertyChanged)
            {
                notifyPropertyChanged.PropertyChanged -= OnViewModelPropertyChanged;
            }
            base.OnClosed(e);
        }
    }

    /// <summary>
    /// 布尔值到背景色转换器
    /// </summary>
    public class BooleanToBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                return new SolidColorBrush(Color.FromRgb(24, 144, 255)); // #1890FF
            }
            return new SolidColorBrush(Colors.Transparent);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到边框厚度转换器
    /// </summary>
    public class BooleanToBorderThicknessConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                return new Thickness(2);
            }
            return new Thickness(1);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到前景色转换器
    /// </summary>
    public class BooleanToForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                return new SolidColorBrush(Colors.White);
            }
            return new SolidColorBrush(Color.FromRgb(24, 144, 255)); // #1890FF
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到字体粗细转换器
    /// </summary>
    public class BooleanToFontWeightConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                return FontWeights.Bold;
            }
            return FontWeights.Normal;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
