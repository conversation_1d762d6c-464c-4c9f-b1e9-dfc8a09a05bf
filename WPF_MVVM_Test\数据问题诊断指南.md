# 数据问题诊断指南

## 问题描述

API调用成功，但添加的数据有些不对。需要诊断具体哪些数据字段有问题。

## 诊断工具

### 1. 👁️ 数据预览
点击"👁️ 数据预览"按钮，可以查看：
- 将要发送的完整数据
- 每个任务的详细字段值
- 实际的JSON格式

### 2. 调试日志
在Visual Studio的"输出"窗口中查看详细日志：
- 任务收集过程
- 每个字段的具体值
- API请求和响应

### 3. 字段检查
点击"🔍 字段检查"按钮，检查数据完整性。

## 常见数据问题

### 1. 时间格式问题
**症状**: 时间显示不正确或格式错误
**检查**: 
- `planStartTime` 和 `planEndTime` 的格式
- 时区设置
- 时间范围是否合理

**修复建议**:
```csharp
// 确保时间格式正确
PlanStartTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
```

### 2. GUID格式问题
**症状**: processRouteId或processStepId格式错误
**检查**:
- GUID是否有效
- 是否为空或null

**修复建议**:
```csharp
// 确保GUID有效
var processRouteId = Guid.TryParse(ProcessRouteNumber, out var routeGuid) ? routeGuid : Guid.NewGuid();
```

### 3. 数量字段问题
**症状**: planQuantity为0或负数
**检查**:
- 数量是否大于0
- 数据类型是否正确

**修复建议**:
```csharp
// 确保数量有效
PlanQuantity = task.PlannedQuantity > 0 ? task.PlannedQuantity : PlanQuantity
```

### 4. 字符串字段问题
**症状**: 某些字段为空或显示"默认值"
**检查**:
- 字段是否为空字符串
- 是否使用了默认值

**修复建议**:
```csharp
// 提供有意义的默认值
TaskName = string.IsNullOrEmpty(task.TaskName) ? $"{processStep.ProcessStepName}任务" : task.TaskName
```

### 5. 序号问题
**症状**: sequenceNumber不正确
**检查**:
- 序号是否连续
- 是否从1开始

**修复建议**:
```csharp
// 确保序号正确
SequenceNumber = allTasks.Count + 1
```

## 诊断步骤

### 步骤1: 使用数据预览
1. 点击"👁️ 数据预览"按钮
2. 检查每个任务的字段值
3. 对比与期望值的差异

### 步骤2: 查看调试日志
1. 打开Visual Studio
2. 运行应用程序
3. 打开"输出"窗口
4. 点击"✅ 确定排产"
5. 查看详细日志

### 步骤3: 对比curl命令
将预览的数据与curl命令中的示例数据对比：
```json
{
  "tasks": [
    {
      "sequenceNumber": 0,
      "taskNumber": "string",
      "taskName": "string",
      "productionOrderId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "stationName": "string",
      "processCode": "string",
      "processName": "string",
      "processFlow": "string",
      "processType": "string",
      "taskColor": "string",
      "planQuantity": 0.01,
      "planStartTime": "2025-08-01T14:09:51.930Z",
      "planEndTime": "2025-08-01T14:09:51.930Z",
      "planDuration": 0.01,
      "status": "string",
      "processRouteId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "processStepId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "priority": 4,
      "remarks": "string"
    }
  ]
}
```

## 具体问题修复

### 1. 如果时间格式不对
```csharp
// 修改时间格式
PlanStartTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
PlanEndTime = DateTime.Now.AddHours(2).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
```

### 2. 如果GUID格式不对
```csharp
// 使用固定的GUID进行测试
ProcessRouteId = Guid.Parse("3fa85f64-5717-4562-b3fc-2c963f66afa6"),
ProcessStepId = Guid.Parse("3fa85f64-5717-4562-b3fc-2c963f66afa6")
```

### 3. 如果数量不对
```csharp
// 使用固定的测试数量
PlanQuantity = 100.0m
```

### 4. 如果字符串字段不对
```csharp
// 使用有意义的字符串
TaskNumber = "TEST_TASK_001",
TaskName = "测试任务",
StationName = "测试工位",
ProcessCode = "TEST_PROCESS",
ProcessName = "测试工序",
ProcessFlow = "测试工艺流程",
ProcessType = "测试类型",
TaskColor = "#1890FF",
Status = "待开工",
Remarks = "测试任务备注"
```

## 验证修复

### 1. 重新预览数据
点击"👁️ 数据预览"按钮，确认修复后的数据正确。

### 2. 测试API调用
点击"🧪 测试API"按钮，确认API调用成功。

### 3. 检查后端数据
在后端系统中查看添加的数据是否正确。

## 调试技巧

### 1. 逐步调试
- 先修复一个字段
- 测试确认正确
- 再修复下一个字段

### 2. 对比测试
- 使用curl命令测试
- 对比结果差异
- 找出问题所在

### 3. 日志分析
- 查看详细的调试日志
- 分析数据流
- 定位问题源头

## 总结

通过以下步骤可以准确定位和修复数据问题：

1. **使用数据预览工具** - 查看完整的数据结构
2. **对比curl命令** - 确认格式正确
3. **查看调试日志** - 分析数据流
4. **逐步修复** - 一个字段一个字段地修复
5. **验证结果** - 确认修复成功

请先使用"👁️ 数据预览"按钮查看具体哪些数据不对，然后我们可以针对性地修复。 