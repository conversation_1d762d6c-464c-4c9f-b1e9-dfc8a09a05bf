using System;

namespace WPF_MVVM_Test.MVVM_Model.ProductionOrder
{
    /// <summary>
    /// 更新生产工单状态请求模型
    /// </summary>
    public class UpdateProductionOrderStatusRequest
    {
        /// <summary>
        /// 生产工单ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdatedBy { get; set; } = string.Empty;
    }
} 