using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace WPF_MVVM_Test.MVVM_View.Converters
{
    /// <summary>
    /// 布尔值到前景色的转换器
    /// </summary>
    public class BooleanToForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return isSelected ? Brushes.White : Brushes.Black;
            }
            
            return Brushes.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 