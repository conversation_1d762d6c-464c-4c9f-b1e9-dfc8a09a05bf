<UserControl x:Class="WPF_MVVM_Test.MVVM_View.UserControl.BomManagement"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel.Bom"
             mc:Ignorable="d"
             d:DesignHeight="600"
        d:DesignWidth="1000">

    <UserControl.DataContext>
        <vm:BomManagementViewModel/>
    </UserControl.DataContext>



    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <!-- 搜索区域 -->
            <RowDefinition Height="*"/>
            <!-- 数据表格区域 -->
            <RowDefinition Height="Auto"/>
            <!-- 分页区域 -->
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <materialDesign:Card Grid.Row="0"
                Margin="10"
                Padding="15">
            <StackPanel>
                <TextBlock Text="BOM管理"
                           FontSize="20"
                           FontWeight="Bold"
                           Margin="0,0,0,15"
                           Foreground="#2E3440"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 产品名称搜索 -->
                    <TextBox Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="产品名称"
                             Text="{Binding ProductNameFilter, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"
                             Height="60"/>

                    <!-- BOM编号搜索 -->
                    <TextBox Grid.Column="1"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="BOM编号"
                             Text="{Binding BomNumberFilter, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"
                             Height="60"/>

                    <!-- 搜索按钮 -->
                    <Button Grid.Column="2"
                            Command="{Binding SearchCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="搜索"
                            Height="40"
                            Width="70"
                            Margin="0,0,10,0"/>

                    <!-- 刷新按钮 -->
                    <Button Grid.Column="3"
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="刷新"
                            Height="40"
                            Width="70"
                            Margin="0,0,10,0"/>

                    <!-- 新增按钮 -->
                    <Button Grid.Column="4"
                            Command="{Binding AddBomCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="新增"
                            Height="40"
                            Width="70"/>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- 数据表格区域 -->
        <materialDesign:Card Grid.Row="1"
                Margin="10,0,10,10">
            <Grid>
                <!-- 加载指示器 -->
                <ProgressBar IsIndeterminate="True"
                             VerticalAlignment="Top"
                             Height="4">
                    <ProgressBar.Style>
                        <Style TargetType="ProgressBar">
                            <Setter Property="Visibility"
                                    Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}"
                                        Value="True">
                                    <Setter Property="Visibility"
                                            Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ProgressBar.Style>
                </ProgressBar>

                <!-- 无数据提示 -->
                <StackPanel HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility"
                                    Value="Collapsed"/>
                            <Style.Triggers>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding IsLoading}"
                                                Value="False"/>
                                        <Condition Binding="{Binding BomItems.Count}"
                                                Value="0"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Visibility"
                                            Value="Visible"/>
                                </MultiDataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>

                    <materialDesign:PackIcon Kind="FileDocumentMultipleOutline"
                                             Width="64"
                                             Height="64"
                                             Foreground="#CCCCCC"
                                             Margin="0,0,0,20"/>
                    <TextBlock Text="暂无BOM数据"
                               FontSize="16"
                               Foreground="#999999"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="请尝试调整搜索条件或刷新数据"
                               FontSize="12"
                               Foreground="#CCCCCC"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                </StackPanel>

                <!-- 数据表格 -->
                <DataGrid ItemsSource="{Binding BomItems}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F8F9FA"
                          FontSize="12"
                        SelectionChanged="DataGrid_SelectionChanged_1">

                    <DataGrid.Columns>
                        <!-- BOM编号 -->
                        <DataGridTextColumn Header="BOM编号"
                                            Binding="{Binding BomNumber}"
                                            Width="120"/>

                        <!-- 产品名称 -->
                        <DataGridTextColumn Header="产品名称"
                                            Binding="{Binding ProductName}"
                                            Width="150"/>

                        <!-- 版本 -->
                        <DataGridTextColumn Header="版本"
                                            Binding="{Binding Version}"
                                            Width="80"/>

                        <!-- 颜色代码 -->
                        <DataGridTextColumn Header="颜色代码"
                                            Binding="{Binding ColorCode}"
                                            Width="100"/>

                        <!-- 单位 -->
                        <DataGridTextColumn Header="单位"
                                            Binding="{Binding Unit}"
                                            Width="60"/>

                        <!-- 日产量 -->
                        <DataGridTextColumn Header="日产量"
                                            Binding="{Binding DailyOutput, StringFormat=F3}"
                                            Width="80"/>

                        <!-- 工艺路线 -->
                        <DataGridTextColumn Header="工艺路线"
                                            Binding="{Binding ProcessRouteName}"
                                            Width="120"/>

                        <!-- 项目数量 -->
                        <DataGridTextColumn Header="项目数量"
                                            Binding="{Binding ItemCount}"
                                            Width="80"/>

                        <!-- 是否默认 -->
                        <DataGridTemplateColumn Header="默认"
                                Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <materialDesign:PackIcon Kind="Check"
                                                             Foreground="Green">
                                        <materialDesign:PackIcon.Style>
                                            <Style TargetType="materialDesign:PackIcon">
                                                <Setter Property="Visibility"
                                                        Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsDefault}"
                                                            Value="True">
                                                        <Setter Property="Visibility"
                                                                Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </materialDesign:PackIcon.Style>
                                    </materialDesign:PackIcon>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 创建时间 -->
                        <DataGridTextColumn Header="创建时间"
                                            Binding="{Binding CreatedTime, StringFormat=yyyy-MM-dd HH:mm}"
                                            Width="130"/>

                        <!-- 备注 -->
                        <DataGridTextColumn Header="备注"
                                            Binding="{Binding Remark}"
                                            Width="*"/>

                        <!-- 操作列 -->
                        <DataGridTemplateColumn Header="操作"
                                Width="180">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal"
                                            HorizontalAlignment="Center">


                                        <!-- 修改按钮 -->
                                        <Button Command="{Binding DataContext.EditBomCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource MaterialDesignRaisedButton}"
                                                Content="修改"
                                                Height="28"
                                                Width="60"
                                                FontSize="10"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- 分页区域 -->
        <materialDesign:Card Grid.Row="2"
                Margin="10,0,10,10"
                Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 分页信息 -->
                <TextBlock Grid.Column="0"
                           Text="{Binding PageInfo}"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="#666"/>

                <!-- 分页控件 -->
                <StackPanel Grid.Column="1"
                        Orientation="Horizontal"
                        VerticalAlignment="Center">
                    <!-- 首页按钮 -->
                    <Button Command="{Binding FirstPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="首页"
                            Height="32"
                            Width="60"
                            Margin="0,0,5,0"
                            FontSize="11"/>

                    <!-- 上一页按钮 -->
                    <Button Command="{Binding PreviousPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="上一页"
                            Height="32"
                            Width="70"
                            Margin="0,0,5,0"
                            FontSize="11"/>

                    <TextBox Text="{Binding JumpToPage, UpdateSourceTrigger=PropertyChanged}"
                             Width="40"
                             Height="32"
                             FontSize="11"
                             TextAlignment="Center"
                             VerticalContentAlignment="Center"
                             Margin="0,0,5,0">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter"
                                    Command="{Binding GoToPageCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>
                    <TextBlock Text="页"
                               VerticalAlignment="Center"
                               FontSize="11"
                               Margin="0,0,5,0"/>

                    <!-- 下一页按钮 -->
                    <Button Command="{Binding NextPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="下一页"
                            Height="32"
                            Width="60"
                            Margin="5,0,5,0"
                            FontSize="11"/>

                    <!-- 末页按钮 -->
                    <Button Command="{Binding LastPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="末页"
                            Height="32"
                            Width="50"
                            Margin="0,0,0,0"
                            FontSize="11"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>