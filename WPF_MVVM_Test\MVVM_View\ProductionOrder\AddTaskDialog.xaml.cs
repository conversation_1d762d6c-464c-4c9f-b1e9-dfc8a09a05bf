using System.Windows;
using WPF_MVVM_Test.MVVM_ViewModel.ProductionOrder;
using WPF_MVVM_Test.MVVM_Model.Process;

namespace WPF_MVVM_Test.MVVM_View.ProductionOrder
{
    /// <summary>
    /// AddTaskDialog.xaml 的交互逻辑
    /// </summary>
    public partial class AddTaskDialog : Window
    {
        public AddTaskDialogViewModel ViewModel { get; private set; }

        public AddTaskDialog(string productionOrderId, string processStepId, string processRouteId, string processRouteName, decimal planQuantity, DateTime planStartTime, DateTime planEndTime)
        {
            InitializeComponent();
            
            ViewModel = new AddTaskDialogViewModel(productionOrderId, processStepId, processRouteId, processRouteName, planQuantity, planStartTime, planEndTime);
            DataContext = ViewModel;
            
            // 监听对话框结果变化
            ViewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(AddTaskDialogViewModel.DialogResult))
            {
                if (ViewModel.DialogResult)
                {
                    DialogResult = true;
                }
                else
                {
                    DialogResult = false;
                }
                Close();
            }
        }

        /// <summary>
        /// 获取新增的任务数据
        /// </summary>
        /// <returns></returns>
        public ProcessTaskModel? GetNewTask()
        {
            if (ViewModel.DialogResult)
            {
                // 计算计划用时（小时）
                var planDuration = (ViewModel.PlanEndTime - ViewModel.PlanStartTime).TotalHours;
                
                return new ProcessTaskModel
                {
                    Id = System.Guid.NewGuid().ToString(),
                    TaskId = ViewModel.TaskNumber,
                    TaskName = ViewModel.TaskName,
                    StationId = ViewModel.StationId,
                    StationName = ViewModel.SelectedStationName?.Name ?? string.Empty,
                    PlannedQuantity = ViewModel.PlanQuantity,
                    PlannedStartTime = ViewModel.PlanStartTime,
                    PlannedEndTime = ViewModel.PlanEndTime,
                    TaskColor = ViewModel.TaskColor,
                    // 从排产页面获取的数据
                    ProcessStepId = ViewModel.ProcessStepId,
                    ProcessRouteId = ViewModel.ProcessRouteId,
                    ProcessRouteName = ViewModel.ProcessRouteName,
                    ProcessType = string.Empty, // 工艺类型为空
                    PlanDuration = (decimal)planDuration,
                    ActualDuration = null, // 实际用时为空
                    Status = "未开工", // 任务状态为未开工
                    ProductionOrderId = ViewModel.ProductionOrderId,
                    Priority = 2, // 默认优先级为中
                    Remarks = ViewModel.Remarks
                };
            }
            
            return null;
        }
    }
} 